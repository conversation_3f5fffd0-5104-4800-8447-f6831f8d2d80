name: remotedesigner
description: A Flutter application to design virtual IR remotes from images.
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter

  # UI and Image Handling
  image_picker: ^1.0.7
  camera: ^0.10.6

  # ML/OCR
  google_mlkit_text_recognition: ^0.13.0

  # State Management
  provider: ^6.1.2

  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # File System
  path_provider: ^2.1.2

  # Permissions (for camera, storage)
  permission_handler: ^11.3.1

  # File operations
  file_picker: ^8.0.0+1
  share_plus: ^9.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true

  # Assets (will add images/icons later)
  assets:
    - assets/images/
    - assets/icons/
