"-Xallow-no-source-files" "-classpath" "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\remote_designer\\build\\app\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\debug\\processDebugResources\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e531755bb3f2441cd560ec86651a0446\\transformed\\jetified-libs.jar;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\remote_designer\\build\\camera_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\remote_designer\\build\\file_picker\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\remote_designer\\build\\flutter_plugin_android_lifecycle\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\remote_designer\\build\\google_mlkit_commons\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\remote_designer\\build\\google_mlkit_text_recognition\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\remote_designer\\build\\image_picker_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\remote_designer\\build\\path_provider_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\remote_designer\\build\\permission_handler_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\remote_designer\\build\\share_plus\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc377edc38de34a5cf66da0fbb724656\\transformed\\jetified-flutter_embedding_debug-1.0.0-1e9a811bf8e70466596bcf0ea3a8b5adb5f17f7f.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2f92004bc81baf63a2a58573254a57c3\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\af3dd9b87b6ce19f19d73457a1391659\\transformed\\jetified-activity-1.10.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c726e9d6d3dc2bd5f17d8ba5464300a1\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3fe40e4a6d413c2a8237834d511bfc36\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f4274c893c02d275acd9a46f445b18c2\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f9d3c34f46a2bd8e9dd4f5a489ff526d\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3ec0e535fd5b0c3e00e6449c8728322b\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e66aeda6d824e698cc60ebeb719ce99f\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dfdd1a42e2d5f4addc5b9b5384882723\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a447b361a20f1cefc9c7141be8c6005b\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a4b3b3d50b28a4579a36b9319ea0c1eb\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b9f8c202f499955d0e9d4a9ffdeeb878\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b15ef98358f546d09ba8e14ce2531d06\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\23e37acd8953c59704b77851d9d43dd4\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1203d25ec56057f47a5858eb8f90ea07\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1be93b58a2e1e2af2d52d20abc91b52a\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9b0c27fc2a1085103dc6f4c23ebfaf18\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d43352186f32121e5a0e5a09b74f0582\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3706736a8e71064e1fa0fd78d0643168\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\22ebf9ffdaa860829d0770962cdda873\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f43134103f8b62b72c2b70f9a252ea67\\transformed\\jetified-annotation-jvm-1.9.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b61d61091e0c0917a2237e075a702722\\transformed\\jetified-core-viewtree-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7a210556717f8ff580ee62fc7503ec1c\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\78156b0a5ca6900477efb810e3b5f0d5\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5a3909e312719c3cf413c7a18710b66b\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4e833f3aee8cf4035f2ae4478833e1e9\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8f9c75f70d67331e83e1ce531101f815\\transformed\\jetified-kotlin-stdlib-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fff5d36d8c855366a596971afc95097f\\transformed\\jetified-arm64_v8a_debug-1.0.0-1e9a811bf8e70466596bcf0ea3a8b5adb5f17f7f.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8eaa1bec644ce27cc4da6dce2eed78eb\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3cbd22ef53167dad97ae3e0318c859ba\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\089a82918e4d334c740fda8bf54ba693\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cae389e9d39d754111bd2479600ae724\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-36\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\35.0.0\\core-lambda-stubs.jar" "-d" "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\remote_designer\\build\\app\\tmp\\kotlin-classes\\debug" "-jvm-target" "11" "-module-name" "app_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "-Xuse-inline-scopes-numbers" "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\remote_designer\\android\\app\\src\\main\\java\\io\\flutter\\plugins\\GeneratedPluginRegistrant.java" "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\remote_designer\\android\\app\\src\\main\\kotlin\\com\\example\\remotedesigner\\MainActivity.kt"