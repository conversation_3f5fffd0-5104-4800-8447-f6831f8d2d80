import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/remote_profile.dart';
import '../models/button.dart';
import '../services/ir_service.dart';
import '../services/storage_service.dart';

class RemotePlayerScreen extends StatefulWidget {
  final RemoteProfile remote;

  const RemotePlayerScreen({
    super.key,
    required this.remote,
  });

  @override
  State<RemotePlayerScreen> createState() => _RemotePlayerScreenState();
}

class _RemotePlayerScreenState extends State<RemotePlayerScreen> {
  final IrService _irService = IrService();
  final StorageService _storageService = StorageService();
  String? _lastPressedButtonId;
  String _statusMessage = 'Ready';
  bool _isTransmitting = false;

  @override
  void initState() {
    super.initState();
    _storageService.init();
  }

  Future<void> _pressButton(Button button) async {
    if (_isTransmitting) return;

    setState(() {
      _lastPressedButtonId = button.id;
      _isTransmitting = true;
      _statusMessage = 'Transmitting ${button.label}...';
    });

    // Haptic feedback
    HapticFeedback.lightImpact();

    try {
      if (button.ir != null) {
        final success = await _irService.transmitIr(button.ir!);
        setState(() {
          _statusMessage = success 
              ? '${button.label} transmitted successfully'
              : 'Failed to transmit ${button.label}';
        });
      } else {
        setState(() {
          _statusMessage = '${button.label} pressed (no IR code assigned)';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Error: $e';
      });
    } finally {
      setState(() {
        _isTransmitting = false;
      });

      // Clear the pressed state after a short delay
      Future.delayed(const Duration(milliseconds: 200), () {
        if (mounted) {
          setState(() {
            _lastPressedButtonId = null;
          });
        }
      });
    }
  }

  Color _parseColor(String colorString) {
    try {
      return Color(int.parse(colorString.replaceFirst('#', '0xff')));
    } catch (e) {
      return Colors.blue;
    }
  }

  IconData? _getIconData(String? iconName) {
    if (iconName == null) return null;
    
    switch (iconName.toLowerCase()) {
      case 'power':
        return Icons.power_settings_new;
      case 'volume_up':
        return Icons.volume_up;
      case 'volume_down':
        return Icons.volume_down;
      case 'volume_off':
        return Icons.volume_off;
      case 'keyboard_arrow_up':
        return Icons.keyboard_arrow_up;
      case 'keyboard_arrow_down':
        return Icons.keyboard_arrow_down;
      case 'keyboard_arrow_left':
        return Icons.keyboard_arrow_left;
      case 'keyboard_arrow_right':
        return Icons.keyboard_arrow_right;
      case 'menu':
        return Icons.menu;
      case 'check_circle':
        return Icons.check_circle;
      case 'play_arrow':
        return Icons.play_arrow;
      case 'pause':
        return Icons.pause;
      case 'stop':
        return Icons.stop;
      case 'fast_forward':
        return Icons.fast_forward;
      case 'fast_rewind':
        return Icons.fast_rewind;
      case 'skip_next':
        return Icons.skip_next;
      case 'skip_previous':
        return Icons.skip_previous;
      case 'home':
        return Icons.home;
      case 'settings':
        return Icons.settings;
      default:
        return Icons.radio_button_unchecked;
    }
  }

  Widget _buildRemoteButton(Button button) {
    final isPressed = _lastPressedButtonId == button.id;
    final hasIrCode = button.ir != null;
    
    return Positioned(
      left: button.bbox.x,
      top: button.bbox.y,
      child: GestureDetector(
        onTap: () => _pressButton(button),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 100),
          width: button.bbox.w,
          height: button.bbox.h,
          decoration: BoxDecoration(
            color: isPressed 
                ? _parseColor(button.style.bgColor).withOpacity(0.7)
                : _parseColor(button.style.bgColor),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: hasIrCode ? Colors.green.withOpacity(0.5) : Colors.grey.withOpacity(0.3),
              width: hasIrCode ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(isPressed ? 0.1 : 0.3),
                blurRadius: isPressed ? 2 : 4,
                offset: Offset(isPressed ? 1 : 2, isPressed ? 1 : 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              // Button content
              Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (button.icon != null) ...[
                      Icon(
                        _getIconData(button.icon),
                        color: _parseColor(button.style.textColor),
                        size: button.bbox.h > 40 ? 20 : 16,
                      ),
                      if (button.bbox.h > 40)
                        const SizedBox(height: 2),
                    ],
                    if (button.bbox.h > 30)
                      Text(
                        button.label,
                        style: TextStyle(
                          color: _parseColor(button.style.textColor),
                          fontSize: button.bbox.h > 40 ? 12 : 10,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),
              
              // IR indicator
              if (hasIrCode)
                Positioned(
                  top: 2,
                  right: 2,
                  child: Container(
                    width: 6,
                    height: 6,
                    decoration: const BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              
              // Transmission indicator
              if (_isTransmitting && isPressed)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.remote.deviceName),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Remote Info'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Device: ${widget.remote.deviceName}'),
                      Text('Buttons: ${widget.remote.buttons.length}'),
                      Text('IR Codes: ${widget.remote.buttons.where((b) => b.ir != null).length}'),
                      const SizedBox(height: 8),
                      const Text('Legend:'),
                      const Row(
                        children: [
                          Icon(Icons.circle, color: Colors.green, size: 12),
                          SizedBox(width: 4),
                          Text('Has IR code', style: TextStyle(fontSize: 12)),
                        ],
                      ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('OK'),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Status bar
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
            ),
            child: Row(
              children: [
                Icon(
                  _isTransmitting ? Icons.radio : Icons.radio_button_unchecked,
                  color: _isTransmitting ? Colors.green : Colors.grey,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _statusMessage,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
                if (_isTransmitting)
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
          ),
          
          // Remote interface
          Expanded(
            child: FutureBuilder<String>(
              future: _storageService.getImagePath(widget.remote.sourceImage),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  final imageFile = File(snapshot.data!);
                  if (imageFile.existsSync()) {
                    return Stack(
                      children: [
                        // Background image
                        Center(
                          child: Image.file(
                            imageFile,
                            fit: BoxFit.contain,
                          ),
                        ),
                        
                        // Button overlays
                        ...widget.remote.buttons.map((button) => _buildRemoteButton(button)).toList(),
                      ],
                    );
                  }
                }
                
                // Fallback: Show buttons without background image
                return Container(
                  color: Colors.grey[200],
                  child: Stack(
                    children: widget.remote.buttons.map((button) => _buildRemoteButton(button)).toList(),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
