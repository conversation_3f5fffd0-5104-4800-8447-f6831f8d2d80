import 'dart:convert';

class Button {
  final String id;
  final String label;
  final String? icon;
  final BoundingBox bbox;
  final Style style;
  final IrCode? ir;

  <PERSON><PERSON>({
    required this.id,
    required this.label,
    this.icon,
    required this.bbox,
    required this.style,
    this.ir,
  });

  factory Button.fromJson(Map<String, dynamic> json) {
    return Button(
      id: json['id'] as String,
      label: json['label'] as String,
      icon: json['icon'] as String?,
      bbox: BoundingBox.fromJson(json['bbox'] as Map<String, dynamic>),
      style: Style.fromJson(json['style'] as Map<String, dynamic>),
      ir: json['ir'] != null ? IrCode.fromJson(json['ir'] as Map<String, dynamic>) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'label': label,
      'icon': icon,
      'bbox': bbox.toJson(),
      'style': style.toJson(),
      'ir': ir?.to<PERSON><PERSON>(),
    };
  }
}

class BoundingBox {
  final double x;
  final double y;
  final double w;
  final double h;

  BoundingBox({
    required this.x,
    required this.y,
    required this.w,
    required this.h,
  });

  factory BoundingBox.fromJson(Map<String, dynamic> json) {
    return BoundingBox(
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
      w: (json['w'] as num).toDouble(),
      h: (json['h'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'w': w,
      'h': h,
    };
  }
}

class Style {
  final String bgColor;
  final String textColor;
  final double borderRadius;

  Style({
    required this.bgColor,
    required this.textColor,
    this.borderRadius = 8.0,
  });

  factory Style.fromJson(Map<String, dynamic> json) {
    return Style(
      bgColor: json['bg_color'] as String,
      textColor: json['text_color'] as String,
      borderRadius: (json['border_radius'] as num?)?.toDouble() ?? 8.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'bg_color': bgColor,
      'text_color': textColor,
      'border_radius': borderRadius,
    };
  }
}

class IrCode {
  final String type;
  final int freq;
  final List<int> pattern;

  IrCode({
    required this.type,
    required this.freq,
    required this.pattern,
  });

  factory IrCode.fromJson(Map<String, dynamic> json) {
    return IrCode(
      type: json['type'] as String,
      freq: json['freq'] as int,
      pattern: List<int>.from(json['pattern'] as List),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'freq': freq,
      'pattern': pattern,
    };
  }
}
