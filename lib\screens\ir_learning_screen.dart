import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/button.dart';
import '../services/ir_service.dart';
import '../services/usb_ir_service.dart';
import '../services/remote_provider.dart';
import 'ir_code_browser_screen.dart';

class IrLearningScreen extends StatefulWidget {
  final Button button;

  const IrLearningScreen({
    super.key,
    required this.button,
  });

  @override
  State<IrLearningScreen> createState() => _IrLearningScreenState();
}

class _IrLearningScreenState extends State<IrLearningScreen> {
  final IrService _irService = IrService();
  bool _isLearning = false;
  bool _hasIrCapability = false;
  List<String> _availableMethods = [];
  List<UsbIrDevice> _usbDevices = [];
  UsbIrDevice? _selectedUsbDevice;
  String? _selectedMethod;
  IrCode? _learnedCode;
  String _status = 'Ready to learn IR signal';

  @override
  void initState() {
    super.initState();
    _checkIrCapabilities();
  }

  Future<void> _checkIrCapabilities() async {
    final methods = await _irService.getAvailableIrMethods();
    final hasIr = await _irService.hasIrTransmitter();
    final usbDevices = await _irService.getUsbIrDevices();

    setState(() {
      _availableMethods = methods;
      _usbDevices = usbDevices;
      _hasIrCapability = hasIr || methods.isNotEmpty || usbDevices.isNotEmpty;
      _selectedMethod = methods.isNotEmpty ? methods.first : null;
      _selectedUsbDevice = usbDevices.isNotEmpty ? usbDevices.first : null;
    });

    if (!_hasIrCapability) {
      setState(() {
        _status = 'No IR capability detected. You can still use predefined codes.';
      });
    } else if (usbDevices.isNotEmpty) {
      setState(() {
        _status = 'USB IR device detected. You can learn signals from your remote.';
      });
    }
  }

  Future<void> _startLearning() async {
    if (!_hasIrCapability) {
      _showPredefinedCodesDialog();
      return;
    }

    // Connect to USB device if selected
    if (_selectedUsbDevice != null) {
      final connected = await _irService.connectToUsbDevice(_selectedUsbDevice!);
      if (!connected) {
        setState(() {
          _status = 'Failed to connect to USB device. Check permissions.';
        });
        return;
      }
    }

    setState(() {
      _isLearning = true;
      _status = _selectedUsbDevice != null
          ? 'Point your original remote at the USB receiver and press the button...'
          : 'Point your original remote at the device and press the button...';
    });

    try {
      final learnedCode = await _irService.learnIrSignal();

      if (learnedCode != null) {
        setState(() {
          _learnedCode = learnedCode;
          _status = 'IR signal learned successfully!';
        });
      } else {
        setState(() {
          _status = 'Failed to learn IR signal. Try again or use predefined codes.';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Error learning IR signal: $e';
      });
    } finally {
      setState(() {
        _isLearning = false;
      });
    }
  }

  void _showPredefinedCodesDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select IR Code Source'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.library_books),
              title: const Text('Browse Code Library'),
              subtitle: const Text('Search from comprehensive database'),
              onTap: () {
                Navigator.of(context).pop();
                _showCodeLibrary();
              },
            ),
            ListTile(
              leading: const Icon(Icons.star),
              title: const Text('Quick Common Codes'),
              subtitle: const Text('Popular codes for testing'),
              onTap: () {
                Navigator.of(context).pop();
                _showQuickCodes();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showCodeLibrary() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => IrCodeBrowserScreen(
          onCodeSelected: (irCode) {
            setState(() {
              _learnedCode = irCode;
              _status = 'IR code selected from library';
            });
          },
        ),
      ),
    );
  }

  void _showQuickCodes() {
    final predefinedCodes = _irService.getPredefinedIrCodes();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Quick Common Codes'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: predefinedCodes.keys.length,
            itemBuilder: (context, index) {
              final key = predefinedCodes.keys.elementAt(index);
              final code = predefinedCodes[key]!;

              return ListTile(
                title: Text(key.replaceAll('_', ' ').toUpperCase()),
                subtitle: Text('${code.type} - ${code.freq}Hz'),
                onTap: () {
                  setState(() {
                    _learnedCode = code;
                    _status = 'Quick code selected: $key';
                  });
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Future<void> _testIrCode() async {
    if (_learnedCode == null) return;

    setState(() {
      _status = 'Testing IR signal...';
    });

    try {
      final success = await _irService.transmitIr(_learnedCode!);
      setState(() {
        _status = success 
            ? 'IR signal transmitted successfully!' 
            : 'Failed to transmit IR signal.';
      });
    } catch (e) {
      setState(() {
        _status = 'Error transmitting IR signal: $e';
      });
    }
  }

  void _saveIrCode() {
    if (_learnedCode == null) return;

    final updatedButton = Button(
      id: widget.button.id,
      label: widget.button.label,
      icon: widget.button.icon,
      bbox: widget.button.bbox,
      style: widget.button.style,
      ir: _learnedCode,
    );

    context.read<RemoteProvider>().updateButtonInCurrentRemote(
      widget.button.id,
      updatedButton,
    );

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('IR code saved to button!')),
    );

    Navigator.of(context).pop();
  }

  void _clearIrCode() {
    setState(() {
      _learnedCode = null;
      _status = 'IR code cleared. Ready to learn new signal.';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Learn IR - ${widget.button.label}'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Button preview
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      'Learning IR for:',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: 80,
                      height: 50,
                      decoration: BoxDecoration(
                        color: _parseColor(widget.button.style.bgColor),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Text(
                          widget.button.label,
                          style: TextStyle(
                            color: _parseColor(widget.button.style.textColor),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // IR capability status
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'IR Capabilities:',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    if (_availableMethods.isEmpty && _usbDevices.isEmpty)
                      const Text(
                        'No IR transmitter detected',
                        style: TextStyle(color: Colors.orange),
                      )
                    else ...[
                      ..._availableMethods.map((method) => Row(
                        children: [
                          const Icon(Icons.check, color: Colors.green, size: 16),
                          const SizedBox(width: 8),
                          Text(method),
                        ],
                      )).toList(),
                      if (_usbDevices.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Text(
                          'USB IR Devices (${_usbDevices.length}):',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        ..._usbDevices.map((device) => Padding(
                          padding: const EdgeInsets.only(left: 16, top: 4),
                          child: Row(
                            children: [
                              Radio<UsbIrDevice>(
                                value: device,
                                groupValue: _selectedUsbDevice,
                                onChanged: (UsbIrDevice? value) {
                                  setState(() {
                                    _selectedUsbDevice = value;
                                  });
                                },
                              ),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(device.name),
                                    Text(
                                      'VID: ${device.vendorId.toRadixString(16).toUpperCase()}, '
                                      'PID: ${device.productId.toRadixString(16).toUpperCase()}',
                                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        )).toList(),
                      ],
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Status
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status:',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(_status),
                    if (_isLearning)
                      const Padding(
                        padding: EdgeInsets.only(top: 8.0),
                        child: LinearProgressIndicator(),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Learned code info
            if (_learnedCode != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Learned IR Code:',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text('Type: ${_learnedCode!.type}'),
                      Text('Frequency: ${_learnedCode!.freq} Hz'),
                      Text('Pattern Length: ${_learnedCode!.pattern.length}'),
                    ],
                  ),
                ),
              ),

            const Spacer(),

            // Action buttons
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                ElevatedButton.icon(
                  onPressed: _isLearning ? null : _startLearning,
                  icon: const Icon(Icons.settings_remote),
                  label: Text(_hasIrCapability ? 'Learn IR Signal' : 'Use Predefined Code'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                
                if (_learnedCode != null) ...[
                  ElevatedButton.icon(
                    onPressed: _testIrCode,
                    icon: const Icon(Icons.play_arrow),
                    label: const Text('Test IR Signal'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  ElevatedButton.icon(
                    onPressed: _saveIrCode,
                    icon: const Icon(Icons.save),
                    label: const Text('Save IR Code'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  TextButton.icon(
                    onPressed: _clearIrCode,
                    icon: const Icon(Icons.clear),
                    label: const Text('Clear'),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _parseColor(String colorString) {
    try {
      return Color(int.parse(colorString.replaceFirst('#', '0xff')));
    } catch (e) {
      return Colors.blue;
    }
  }
}
