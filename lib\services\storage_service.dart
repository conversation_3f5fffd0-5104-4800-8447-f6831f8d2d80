import 'dart:convert';
import 'dart:io';
import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';
import '../models/remote_profile.dart';

class StorageService {
  static const String _remotesBoxName = 'remotes';
  late Box<String> _remotesBox;

  Future<void> init() async {
    try {
      _remotesBox = await Hive.openBox<String>(_remotesBoxName);
    } catch (e) {
      print('Error initializing storage: $e');
      // Try to delete corrupted box and recreate
      try {
        await Hive.deleteBoxFromDisk(_remotesBoxName);
        _remotesBox = await Hive.openBox<String>(_remotesBoxName);
      } catch (e2) {
        print('Error recreating storage box: $e2');
        rethrow;
      }
    }
  }

  // Save remote profile to local storage
  Future<void> saveRemote(RemoteProfile remote) async {
    await _remotesBox.put(remote.remoteId, jsonEncode(remote.toJson()));
  }

  // Load all saved remotes
  Future<List<RemoteProfile>> loadRemotes() async {
    final List<RemoteProfile> remotes = [];
    for (final key in _remotesBox.keys) {
      final jsonString = _remotesBox.get(key);
      if (jsonString != null) {
        try {
          final json = jsonDecode(jsonString) as Map<String, dynamic>;
          remotes.add(RemoteProfile.fromJson(json));
        } catch (e) {
          print('Error loading remote $key: $e');
        }
      }
    }
    return remotes;
  }

  // Delete a remote
  Future<void> deleteRemote(String remoteId) async {
    await _remotesBox.delete(remoteId);
  }

  // Export remote to JSON file
  Future<File> exportRemote(RemoteProfile remote) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/${remote.remoteId}.json');
    await file.writeAsString(jsonEncode(remote.toJson()));
    return file;
  }

  // Import remote from JSON file
  Future<RemoteProfile> importRemote(File file) async {
    final jsonString = await file.readAsString();
    final json = jsonDecode(jsonString) as Map<String, dynamic>;
    return RemoteProfile.fromJson(json);
  }

  // Save image file locally
  Future<String> saveImage(File imageFile, String remoteId) async {
    final directory = await getApplicationDocumentsDirectory();
    final imagesDir = Directory('${directory.path}/images');
    if (!await imagesDir.exists()) {
      await imagesDir.create(recursive: true);
    }
    
    final fileName = '${remoteId}_${DateTime.now().millisecondsSinceEpoch}.jpg';
    final savedFile = File('${imagesDir.path}/$fileName');
    await imageFile.copy(savedFile.path);
    return fileName;
  }

  // Get image file path
  Future<String> getImagePath(String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/images/$fileName';
  }
}
