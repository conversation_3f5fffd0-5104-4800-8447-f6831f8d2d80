import 'dart:io';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import '../models/button.dart';
import 'ir_code_library.dart';

class DeviceDetectionResult {
  final String? detectedBrand;
  final String? detectedDeviceType;
  final double confidence;
  final Map<String, IrCode> suggestedCodes;
  final List<String> detectedTexts;

  DeviceDetectionResult({
    this.detectedBrand,
    this.detectedDeviceType,
    required this.confidence,
    required this.suggestedCodes,
    required this.detectedTexts,
  });
}

class DeviceDetectionService {
  final TextRecognizer _textRecognizer = TextRecognizer();

  // Brand keywords and their variations
  static final Map<String, List<String>> _brandKeywords = {
    'Samsung': ['samsung', 'sam sung', 'samsang', 'samsyng'],
    'LG': ['lg', 'l.g', 'life\'s good', 'lifes good'],
    'Sony': ['sony', 'son y', 'soni'],
    'Panasonic': ['panasonic', 'pana sonic', 'matsushita'],
    'Toshiba': ['toshiba', 'toshi ba'],
    'Sharp': ['sharp', 'shar p'],
    'Philips': ['philips', 'phil ips'],
    'TCL': ['tcl', 't.c.l'],
    'Hisense': ['hisense', 'hi sense'],
    'Xiaomi': ['xiaomi', 'xiao mi', 'mi'],
  };

  // Device type keywords
  static final Map<String, List<String>> _deviceTypeKeywords = {
    'TV': ['tv', 'television', 'smart tv', 'led tv', 'lcd tv', 'oled', 'qled'],
    'AC': ['ac', 'air conditioner', 'air con', 'aircon', 'cooling', 'heating'],
    'STB': ['set top box', 'stb', 'cable box', 'satellite', 'decoder'],
    'DVD': ['dvd', 'blu-ray', 'player', 'disc'],
    'Audio': ['audio', 'speaker', 'sound', 'music', 'stereo', 'amplifier'],
  };

  // Common button patterns for different device types
  static final Map<String, List<String>> _deviceButtonPatterns = {
    'TV': ['power', 'volume', 'channel', 'mute', 'menu', 'source', 'input'],
    'AC': ['power', 'temp', 'mode', 'fan', 'timer', 'swing'],
    'STB': ['power', 'channel', 'guide', 'info', 'record', 'play', 'pause'],
    'DVD': ['power', 'play', 'pause', 'stop', 'next', 'prev', 'menu'],
    'Audio': ['power', 'volume', 'bass', 'treble', 'mode', 'input'],
  };

  // Detect device brand and type from image
  Future<DeviceDetectionResult> detectDevice(File imageFile) async {
    try {
      final inputImage = InputImage.fromFile(imageFile);
      final recognizedText = await _textRecognizer.processImage(inputImage);
      
      final allTexts = <String>[];
      final textBlocks = <String>[];
      
      // Extract all text from the image
      for (final textBlock in recognizedText.blocks) {
        for (final textLine in textBlock.lines) {
          final text = textLine.text.toLowerCase().trim();
          allTexts.add(text);
          textBlocks.add(text);
        }
      }
      
      final fullText = allTexts.join(' ').toLowerCase();
      
      // Detect brand
      String? detectedBrand;
      double brandConfidence = 0.0;
      
      for (final entry in _brandKeywords.entries) {
        final brand = entry.key;
        final keywords = entry.value;
        
        for (final keyword in keywords) {
          if (fullText.contains(keyword)) {
            final confidence = _calculateBrandConfidence(keyword, fullText);
            if (confidence > brandConfidence) {
              detectedBrand = brand;
              brandConfidence = confidence;
            }
          }
        }
      }
      
      // Detect device type
      String? detectedDeviceType;
      double deviceTypeConfidence = 0.0;
      
      for (final entry in _deviceTypeKeywords.entries) {
        final deviceType = entry.key;
        final keywords = entry.value;
        
        for (final keyword in keywords) {
          if (fullText.contains(keyword)) {
            final confidence = _calculateDeviceTypeConfidence(keyword, fullText, allTexts);
            if (confidence > deviceTypeConfidence) {
              detectedDeviceType = deviceType;
              deviceTypeConfidence = confidence;
            }
          }
        }
      }
      
      // If no explicit device type found, try to infer from button patterns
      if (detectedDeviceType == null) {
        detectedDeviceType = _inferDeviceTypeFromButtons(allTexts);
        deviceTypeConfidence = 0.5; // Lower confidence for inferred type
      }
      
      // Get suggested codes
      Map<String, IrCode> suggestedCodes = {};
      if (detectedBrand != null && detectedDeviceType != null) {
        suggestedCodes = IrCodeLibrary.getAllCodes(detectedBrand, detectedDeviceType);
      } else if (detectedBrand != null) {
        // Try TV as default device type
        suggestedCodes = IrCodeLibrary.getAllCodes(detectedBrand, 'TV');
      } else if (detectedDeviceType != null) {
        // Get codes from Samsung as default brand
        suggestedCodes = IrCodeLibrary.getAllCodes('Samsung', detectedDeviceType);
      }
      
      final overallConfidence = (brandConfidence + deviceTypeConfidence) / 2;
      
      return DeviceDetectionResult(
        detectedBrand: detectedBrand,
        detectedDeviceType: detectedDeviceType,
        confidence: overallConfidence,
        suggestedCodes: suggestedCodes,
        detectedTexts: allTexts,
      );
      
    } catch (e) {
      print('Error in device detection: $e');
      return DeviceDetectionResult(
        confidence: 0.0,
        suggestedCodes: {},
        detectedTexts: [],
      );
    }
  }

  // Calculate brand confidence based on keyword match
  double _calculateBrandConfidence(String keyword, String fullText) {
    double confidence = 0.3; // Base confidence
    
    // Exact match gets higher confidence
    if (fullText.contains(' $keyword ') || fullText.startsWith('$keyword ') || fullText.endsWith(' $keyword')) {
      confidence += 0.4;
    }
    
    // Multiple occurrences increase confidence
    final occurrences = keyword.allMatches(fullText).length;
    confidence += (occurrences - 1) * 0.1;
    
    // Longer keywords get higher confidence
    if (keyword.length > 4) {
      confidence += 0.2;
    }
    
    return confidence.clamp(0.0, 1.0);
  }

  // Calculate device type confidence
  double _calculateDeviceTypeConfidence(String keyword, String fullText, List<String> allTexts) {
    double confidence = 0.3; // Base confidence
    
    // Check for related button patterns
    final deviceType = _deviceTypeKeywords.entries
        .firstWhere((entry) => entry.value.contains(keyword), orElse: () => MapEntry('', []))
        .key;
    
    if (deviceType.isNotEmpty) {
      final expectedButtons = _deviceButtonPatterns[deviceType] ?? [];
      int matchingButtons = 0;
      
      for (final buttonName in expectedButtons) {
        for (final text in allTexts) {
          if (text.contains(buttonName)) {
            matchingButtons++;
            break;
          }
        }
      }
      
      confidence += (matchingButtons / expectedButtons.length) * 0.5;
    }
    
    return confidence.clamp(0.0, 1.0);
  }

  // Infer device type from detected button patterns
  String? _inferDeviceTypeFromButtons(List<String> detectedTexts) {
    final buttonCounts = <String, int>{};
    
    for (final deviceType in _deviceButtonPatterns.keys) {
      final expectedButtons = _deviceButtonPatterns[deviceType]!;
      int matchCount = 0;
      
      for (final buttonName in expectedButtons) {
        for (final text in detectedTexts) {
          if (text.contains(buttonName)) {
            matchCount++;
            break;
          }
        }
      }
      
      buttonCounts[deviceType] = matchCount;
    }
    
    // Return device type with highest button match count
    if (buttonCounts.isNotEmpty) {
      final maxEntry = buttonCounts.entries.reduce((a, b) => a.value > b.value ? a : b);
      if (maxEntry.value >= 2) { // Require at least 2 matching buttons
        return maxEntry.key;
      }
    }
    
    return null;
  }

  // Get smart suggestions based on device name input
  DeviceDetectionResult getSmartSuggestions(String deviceName) {
    final deviceLower = deviceName.toLowerCase();
    String? detectedBrand;
    String? detectedDeviceType;
    
    // Detect brand from device name
    for (final entry in _brandKeywords.entries) {
      final brand = entry.key;
      final keywords = entry.value;
      
      for (final keyword in keywords) {
        if (deviceLower.contains(keyword)) {
          detectedBrand = brand;
          break;
        }
      }
      if (detectedBrand != null) break;
    }
    
    // Detect device type from device name
    for (final entry in _deviceTypeKeywords.entries) {
      final deviceType = entry.key;
      final keywords = entry.value;
      
      for (final keyword in keywords) {
        if (deviceLower.contains(keyword)) {
          detectedDeviceType = deviceType;
          break;
        }
      }
      if (detectedDeviceType != null) break;
    }
    
    // Default to TV if no device type detected
    detectedDeviceType ??= 'TV';
    
    // Get suggested codes
    Map<String, IrCode> suggestedCodes = {};
    if (detectedBrand != null) {
      suggestedCodes = IrCodeLibrary.getAllCodes(detectedBrand, detectedDeviceType);
    } else {
      // Use Samsung as default brand
      suggestedCodes = IrCodeLibrary.getAllCodes('Samsung', detectedDeviceType);
    }
    
    return DeviceDetectionResult(
      detectedBrand: detectedBrand,
      detectedDeviceType: detectedDeviceType,
      confidence: detectedBrand != null ? 0.8 : 0.5,
      suggestedCodes: suggestedCodes,
      detectedTexts: [deviceName],
    );
  }

  void dispose() {
    _textRecognizer.close();
  }
}
