import 'dart:convert';
import 'button.dart';

class RemoteProfile {
  final String remoteId;
  final String deviceName;
  final String sourceImage;
  final double imageW;
  final double imageH;
  final List<Button> buttons;

  RemoteProfile({
    required this.remoteId,
    required this.deviceName,
    required this.sourceImage,
    required this.imageW,
    required this.imageH,
    required this.buttons,
  });

  factory RemoteProfile.fromJson(Map<String, dynamic> json) {
    return RemoteProfile(
      remoteId: json['remote_id'] as String,
      deviceName: json['device_name'] as String,
      sourceImage: json['source_image'] as String,
      imageW: (json['image_w'] as num).toDouble(),
      imageH: (json['image_h'] as num).toDouble(),
      buttons: (json['buttons'] as List)
          .map((b) => Button.fromJson(b as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'remote_id': remoteId,
      'device_name': deviceName,
      'source_image': sourceImage,
      'image_w': imageW,
      'image_h': imageH,
      'buttons': buttons.map((b) => b.toJson()).toList(),
    };
  }

  // Helper to normalize bbox coordinates (0-1 relative to image size)
  RemoteProfile normalizeCoordinates() {
    final normalizedButtons = buttons.map((button) {
      final normX = button.bbox.x / imageW;
      final normY = button.bbox.y / imageH;
      final normW = button.bbox.w / imageW;
      final normH = button.bbox.h / imageH;
      final normBbox = BoundingBox(
        x: normX,
        y: normY,
        w: normW,
        h: normH,
      );
      return Button(
        id: button.id,
        label: button.label,
        icon: button.icon,
        bbox: normBbox,
        style: button.style,
        ir: button.ir,
      );
    }).toList();

    return RemoteProfile(
      remoteId: remoteId,
      deviceName: deviceName,
      sourceImage: sourceImage,
      imageW: 1.0,  // Normalized
      imageH: 1.0,  // Normalized
      buttons: normalizedButtons,
    );
  }
}
