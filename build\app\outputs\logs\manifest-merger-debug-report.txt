-- Merging decision tree log ---
application
INJECTED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:19:5-50:19
INJECTED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\debug\AndroidManifest.xml
MERGED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-33:19
MERGED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-33:19
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77a93e071b342b7344c87d70c4336d1c\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77a93e071b342b7344c87d70c4336d1c\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c2cce33d811c356b28eb01e2ed28f4e\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c2cce33d811c356b28eb01e2ed28f4e\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\312c2df5dd04ad542212dc294df5d7cd\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\312c2df5dd04ad542212dc294df5d7cd\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7912ee62a7ffcad242aa1c9b5b1b3393\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7912ee62a7ffcad242aa1c9b5b1b3393\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c34278bbe928b6aeb2f9e7e8dced1b2a\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c34278bbe928b6aeb2f9e7e8dced1b2a\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec3bed3c7d477c70d95c50194b7855ae\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec3bed3c7d477c70d95c50194b7855ae\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f7be8e0b7187bd3a834fd06c6e10470\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f7be8e0b7187bd3a834fd06c6e10470\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\71f3fea7346749b7f27594f95f34b0a2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\71f3fea7346749b7f27594f95f34b0a2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1bd9e85588430ec51ca2f790d4704753\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1bd9e85588430ec51ca2f790d4704753\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2dc51b4c89795326f1892ca1ee9bca4\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2dc51b4c89795326f1892ca1ee9bca4\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml
manifest
ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:1:1-62:12
MERGED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:1:1-62:12
INJECTED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-35:12
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:camera_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:google_mlkit_text_recognition] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\google_mlkit_text_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:google_mlkit_commons] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\google_mlkit_commons\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77a93e071b342b7344c87d70c4336d1c\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c87a9ed5028bb676b7be77db7955e90\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [com.google.mlkit:text-recognition:16.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:text-recognition-bundled-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ecb430ea600c90220ae498a851d09\transformed\jetified-text-recognition-bundled-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:19.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2795e6cd94cd94565ea370024408f402\transformed\jetified-play-services-mlkit-text-recognition-19.0.1\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c2cce33d811c356b28eb01e2ed28f4e\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\312c2df5dd04ad542212dc294df5d7cd\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7912ee62a7ffcad242aa1c9b5b1b3393\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84cfbdc9da415e926e22677b998bca2a\transformed\jetified-vision-interfaces-16.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c34278bbe928b6aeb2f9e7e8dced1b2a\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec3bed3c7d477c70d95c50194b7855ae\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7e8e148e219468ab943c28c017ddb15\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1928fcfc97c48b7e6272f9cce0561a5\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3c1099445a8641348914188dd3eeb162\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\88ef443f815d0a5dd33aa331c1033668\transformed\jetified-activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9e9d921f4f5b6ad7d3c09569052ffdb7\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\929dc30fdf4d67227a888eff5eed298a\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\60c32d40eaeb72d74b9a401a63802272\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f75b8c602252404344e76eeefb8e3324\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ad53f7395904283a406130c8696bc04\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9bc135a5507b743174c0e273d8c579b4\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f7be8e0b7187bd3a834fd06c6e10470\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d1b93c6fe96ab5bdd75270bb3c9c7ee\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\67757b1b90f78be2a8e9f605893b1b7c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a2ed842872067add618702b3da8bb9c5\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\98d58382faaf6381a8e63b418b440391\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e926bb51d871c2fee4b0d6143fca9546\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b87a000684214c7656b01e30a3210de\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8cd000a7252dcebf36af61be1c986fa1\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b11398e2631803304e8c69c74406132e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cf0e5e9f572056b28a5b1f516fe58c3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.exifinterface:exifinterface:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d219b27b80352d4ec0cc114ad320c6f\transformed\exifinterface-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\71f3fea7346749b7f27594f95f34b0a2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa53b374b50c79695c7071879c3b28dd\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e886ebb8039213989c7ff2825320781c\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\31cadae94ac5b23f61aaf002f1cc4004\transformed\jetified-firebase-components-16.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b561f35de187d767a8a8a440c12d0f88\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aaff095834d665cca861431e36f4e85b\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1bd9e85588430ec51ca2f790d4704753\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a288bcf3f70957db18e7bc12df9f537e\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8cfd2a473567057bbc9daa9e207314e1\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c7f6ccbf712f8c7b050058e276cd1e6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee5fc285c354eec4877738a143fd8819\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\63a48e0e279d39bafcbbe4ec4963a946\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\2545643619de53c76acf0324b9b89bf9\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2dc51b4c89795326f1892ca1ee9bca4\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.TRANSMIT_IR
ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:3:5-70
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:3:22-67
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:6:5-65
MERGED from [:camera_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-65
MERGED from [:camera_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-65
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:7:5-80
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:38
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:38
	android:maxSdkVersion
		ADDED from [:file_picker] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-35
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:7:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:8:5-81
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:9:5-76
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:9:22-73
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:10:5-67
MERGED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:10:5-67
MERGED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:10:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:10:22-64
uses-permission#android.permission.USB_PERMISSION
ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:13:5-73
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:13:22-70
uses-feature#android.hardware.usb.host
ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:14:5-87
	android:required
		ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:14:60-84
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:14:19-59
uses-feature#android.hardware.consumerir
ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:17:5-89
	android:required
		ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:17:62-86
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:17:19-61
queries
ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:56:5-61:15
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-17:15
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-17:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:57:9-60:18
action#android.intent.action.PROCESS_TEXT
ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:58:13-72
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:58:21-70
data
ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:59:13-50
	android:mimeType
		ADDED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:59:19-48
uses-sdk
INJECTED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\debug\AndroidManifest.xml
MERGED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:camera_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:camera_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_mlkit_text_recognition] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\google_mlkit_text_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_mlkit_text_recognition] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\google_mlkit_text_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_mlkit_commons] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\google_mlkit_commons\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_mlkit_commons] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\google_mlkit_commons\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77a93e071b342b7344c87d70c4336d1c\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77a93e071b342b7344c87d70c4336d1c\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c87a9ed5028bb676b7be77db7955e90\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c87a9ed5028bb676b7be77db7955e90\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.mlkit:text-recognition:16.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:text-recognition:16.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:text-recognition-bundled-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ecb430ea600c90220ae498a851d09\transformed\jetified-text-recognition-bundled-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:text-recognition-bundled-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ecb430ea600c90220ae498a851d09\transformed\jetified-text-recognition-bundled-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:19.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2795e6cd94cd94565ea370024408f402\transformed\jetified-play-services-mlkit-text-recognition-19.0.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:19.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2795e6cd94cd94565ea370024408f402\transformed\jetified-play-services-mlkit-text-recognition-19.0.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c2cce33d811c356b28eb01e2ed28f4e\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c2cce33d811c356b28eb01e2ed28f4e\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\312c2df5dd04ad542212dc294df5d7cd\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\312c2df5dd04ad542212dc294df5d7cd\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7912ee62a7ffcad242aa1c9b5b1b3393\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7912ee62a7ffcad242aa1c9b5b1b3393\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84cfbdc9da415e926e22677b998bca2a\transformed\jetified-vision-interfaces-16.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84cfbdc9da415e926e22677b998bca2a\transformed\jetified-vision-interfaces-16.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c34278bbe928b6aeb2f9e7e8dced1b2a\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c34278bbe928b6aeb2f9e7e8dced1b2a\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec3bed3c7d477c70d95c50194b7855ae\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec3bed3c7d477c70d95c50194b7855ae\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7e8e148e219468ab943c28c017ddb15\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7e8e148e219468ab943c28c017ddb15\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1928fcfc97c48b7e6272f9cce0561a5\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1928fcfc97c48b7e6272f9cce0561a5\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3c1099445a8641348914188dd3eeb162\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3c1099445a8641348914188dd3eeb162\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\88ef443f815d0a5dd33aa331c1033668\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\88ef443f815d0a5dd33aa331c1033668\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9e9d921f4f5b6ad7d3c09569052ffdb7\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9e9d921f4f5b6ad7d3c09569052ffdb7\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\929dc30fdf4d67227a888eff5eed298a\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\929dc30fdf4d67227a888eff5eed298a\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\60c32d40eaeb72d74b9a401a63802272\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\60c32d40eaeb72d74b9a401a63802272\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f75b8c602252404344e76eeefb8e3324\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f75b8c602252404344e76eeefb8e3324\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ad53f7395904283a406130c8696bc04\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ad53f7395904283a406130c8696bc04\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9bc135a5507b743174c0e273d8c579b4\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9bc135a5507b743174c0e273d8c579b4\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f7be8e0b7187bd3a834fd06c6e10470\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f7be8e0b7187bd3a834fd06c6e10470\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d1b93c6fe96ab5bdd75270bb3c9c7ee\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d1b93c6fe96ab5bdd75270bb3c9c7ee\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\67757b1b90f78be2a8e9f605893b1b7c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\67757b1b90f78be2a8e9f605893b1b7c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a2ed842872067add618702b3da8bb9c5\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a2ed842872067add618702b3da8bb9c5\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\98d58382faaf6381a8e63b418b440391\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\98d58382faaf6381a8e63b418b440391\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e926bb51d871c2fee4b0d6143fca9546\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e926bb51d871c2fee4b0d6143fca9546\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b87a000684214c7656b01e30a3210de\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b87a000684214c7656b01e30a3210de\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8cd000a7252dcebf36af61be1c986fa1\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8cd000a7252dcebf36af61be1c986fa1\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b11398e2631803304e8c69c74406132e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b11398e2631803304e8c69c74406132e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cf0e5e9f572056b28a5b1f516fe58c3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cf0e5e9f572056b28a5b1f516fe58c3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d219b27b80352d4ec0cc114ad320c6f\transformed\exifinterface-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.exifinterface:exifinterface:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d219b27b80352d4ec0cc114ad320c6f\transformed\exifinterface-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\71f3fea7346749b7f27594f95f34b0a2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\71f3fea7346749b7f27594f95f34b0a2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa53b374b50c79695c7071879c3b28dd\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa53b374b50c79695c7071879c3b28dd\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e886ebb8039213989c7ff2825320781c\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e886ebb8039213989c7ff2825320781c\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\31cadae94ac5b23f61aaf002f1cc4004\transformed\jetified-firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\31cadae94ac5b23f61aaf002f1cc4004\transformed\jetified-firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b561f35de187d767a8a8a440c12d0f88\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b561f35de187d767a8a8a440c12d0f88\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aaff095834d665cca861431e36f4e85b\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aaff095834d665cca861431e36f4e85b\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1bd9e85588430ec51ca2f790d4704753\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1bd9e85588430ec51ca2f790d4704753\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a288bcf3f70957db18e7bc12df9f537e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a288bcf3f70957db18e7bc12df9f537e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8cfd2a473567057bbc9daa9e207314e1\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8cfd2a473567057bbc9daa9e207314e1\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c7f6ccbf712f8c7b050058e276cd1e6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c7f6ccbf712f8c7b050058e276cd1e6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee5fc285c354eec4877738a143fd8819\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee5fc285c354eec4877738a143fd8819\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\63a48e0e279d39bafcbbe4ec4963a946\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\63a48e0e279d39bafcbbe4ec4963a946\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\2545643619de53c76acf0324b9b89bf9\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\2545643619de53c76acf0324b9b89bf9\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2dc51b4c89795326f1892ca1ee9bca4\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2dc51b4c89795326f1892ca1ee9bca4\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\debug\AndroidManifest.xml
provider#dev.fluttercommunity.plus.share.ShareFileProvider
ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
	android:grantUriPermissions
		ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
	android:authorities
		ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
	android:exported
		ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
	android:resource
		ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
receiver#dev.fluttercommunity.plus.share.SharePlusPendingIntent
ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
	android:exported
		ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
intent-filter#action:name:EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
action#EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:*/*
ADDED from [:file_picker] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-16:18
action#android.intent.action.GET_CONTENT
ADDED from [:file_picker] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-72
	android:name
		ADDED from [:file_picker] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:21-69
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
uses-permission#android.permission.RECORD_AUDIO
ADDED from [:camera_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-71
	android:name
		ADDED from [:camera_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-68
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77a93e071b342b7344c87d70c4336d1c\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77a93e071b342b7344c87d70c4336d1c\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77a93e071b342b7344c87d70c4336d1c\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77a93e071b342b7344c87d70c4336d1c\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77a93e071b342b7344c87d70c4336d1c\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77a93e071b342b7344c87d70c4336d1c\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c2cce33d811c356b28eb01e2ed28f4e\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\312c2df5dd04ad542212dc294df5d7cd\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\312c2df5dd04ad542212dc294df5d7cd\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c2cce33d811c356b28eb01e2ed28f4e\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c2cce33d811c356b28eb01e2ed28f4e\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c2cce33d811c356b28eb01e2ed28f4e\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c2cce33d811c356b28eb01e2ed28f4e\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c2cce33d811c356b28eb01e2ed28f4e\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:13:17-114
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\312c2df5dd04ad542212dc294df5d7cd\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\312c2df5dd04ad542212dc294df5d7cd\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\312c2df5dd04ad542212dc294df5d7cd\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:21:17-120
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7912ee62a7ffcad242aa1c9b5b1b3393\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7912ee62a7ffcad242aa1c9b5b1b3393\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7912ee62a7ffcad242aa1c9b5b1b3393\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7912ee62a7ffcad242aa1c9b5b1b3393\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec3bed3c7d477c70d95c50194b7855ae\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec3bed3c7d477c70d95c50194b7855ae\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec3bed3c7d477c70d95c50194b7855ae\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f7be8e0b7187bd3a834fd06c6e10470\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f7be8e0b7187bd3a834fd06c6e10470\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\71f3fea7346749b7f27594f95f34b0a2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\71f3fea7346749b7f27594f95f34b0a2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f7be8e0b7187bd3a834fd06c6e10470\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f7be8e0b7187bd3a834fd06c6e10470\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f7be8e0b7187bd3a834fd06c6e10470\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.remotedesigner.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.remotedesigner.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
