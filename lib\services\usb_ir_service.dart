import 'package:flutter/services.dart';
import '../models/button.dart';

class UsbIrDevice {
  final String name;
  final int vendorId;
  final int productId;
  final String devicePath;
  final bool canTransmit;
  final bool canReceive;

  UsbIrDevice({
    required this.name,
    required this.vendorId,
    required this.productId,
    required this.devicePath,
    required this.canTransmit,
    required this.canReceive,
  });

  factory UsbIrDevice.fromJson(Map<String, dynamic> json) {
    return UsbIrDevice(
      name: json['name'] as String,
      vendorId: json['vendorId'] as int,
      productId: json['productId'] as int,
      devicePath: json['devicePath'] as String,
      canTransmit: json['canTransmit'] as bool,
      canReceive: json['canReceive'] as bool,
    );
  }
}

class UsbIrService {
  static const MethodChannel _channel = MethodChannel('remotedesigner/usb_ir');
  
  // Known USB IR dongles with their vendor/product IDs
  static const List<Map<String, dynamic>> _knownDevices = [
    {
      'name': 'Generic USB IR Transceiver',
      'vendorId': 0x1234,
      'productId': 0x5678,
      'canTransmit': true,
      'canReceive': true,
    },
    {
      'name': 'Arduino-based IR Controller',
      'vendorId': 0x2341,
      'productId': 0x0043,
      'canTransmit': true,
      'canReceive': true,
    },
    {
      'name': 'IrDA USB Adapter',
      'vendorId': 0x067b,
      'productId': 0x2303,
      'canTransmit': true,
      'canReceive': false,
    },
    // Add more known devices here
  ];

  // Check for connected USB IR devices
  Future<List<UsbIrDevice>> getConnectedDevices() async {
    try {
      final result = await _channel.invokeMethod('getConnectedDevices');
      final List<dynamic> devicesData = result['devices'] ?? [];
      
      return devicesData.map((data) => UsbIrDevice.fromJson(data as Map<String, dynamic>)).toList();
    } catch (e) {
      print('Error getting USB devices: $e');
      return [];
    }
  }

  // Request permission to use USB device
  Future<bool> requestPermission(UsbIrDevice device) async {
    try {
      final result = await _channel.invokeMethod('requestPermission', {
        'vendorId': device.vendorId,
        'productId': device.productId,
        'devicePath': device.devicePath,
      });
      return result as bool;
    } catch (e) {
      print('Error requesting USB permission: $e');
      return false;
    }
  }

  // Connect to USB IR device
  Future<bool> connectToDevice(UsbIrDevice device) async {
    try {
      final result = await _channel.invokeMethod('connectToDevice', {
        'vendorId': device.vendorId,
        'productId': device.productId,
        'devicePath': device.devicePath,
      });
      return result as bool;
    } catch (e) {
      print('Error connecting to USB device: $e');
      return false;
    }
  }

  // Disconnect from USB IR device
  Future<bool> disconnectFromDevice() async {
    try {
      final result = await _channel.invokeMethod('disconnectFromDevice');
      return result as bool;
    } catch (e) {
      print('Error disconnecting from USB device: $e');
      return false;
    }
  }

  // Transmit IR signal via USB device
  Future<bool> transmitIrViaUsb(IrCode irCode) async {
    try {
      final result = await _channel.invokeMethod('transmitIrViaUsb', {
        'frequency': irCode.freq,
        'pattern': irCode.pattern,
        'type': irCode.type,
      });
      return result as bool;
    } catch (e) {
      print('Error transmitting IR via USB: $e');
      return false;
    }
  }

  // Learn IR signal via USB device
  Future<IrCode?> learnIrViaUsb({int timeoutSeconds = 10}) async {
    try {
      final result = await _channel.invokeMethod('learnIrViaUsb', {
        'timeoutSeconds': timeoutSeconds,
      });
      
      if (result != null && result['success'] == true) {
        return IrCode(
          type: result['type'] as String,
          freq: result['frequency'] as int,
          pattern: List<int>.from(result['pattern'] as List),
        );
      }
      return null;
    } catch (e) {
      print('Error learning IR via USB: $e');
      return null;
    }
  }

  // Check if device is currently connected
  Future<bool> isDeviceConnected() async {
    try {
      final result = await _channel.invokeMethod('isDeviceConnected');
      return result as bool;
    } catch (e) {
      print('Error checking device connection: $e');
      return false;
    }
  }

  // Get device capabilities
  Future<Map<String, bool>> getDeviceCapabilities() async {
    try {
      final result = await _channel.invokeMethod('getDeviceCapabilities');
      return {
        'canTransmit': result['canTransmit'] as bool? ?? false,
        'canReceive': result['canReceive'] as bool? ?? false,
        'supportedProtocols': result['supportedProtocols'] as bool? ?? false,
      };
    } catch (e) {
      print('Error getting device capabilities: $e');
      return {'canTransmit': false, 'canReceive': false, 'supportedProtocols': false};
    }
  }

  // Test USB device connection
  Future<bool> testDeviceConnection() async {
    try {
      final result = await _channel.invokeMethod('testDeviceConnection');
      return result as bool;
    } catch (e) {
      print('Error testing device connection: $e');
      return false;
    }
  }

  // Scan all connected USB devices for IR capability
  Future<List<Map<String, dynamic>>> scanAllConnectedDevices() async {
    try {
      final result = await _channel.invokeMethod('scanAllConnectedDevices');
      return List<Map<String, dynamic>>.from(result.cast<Map<String, dynamic>>());
    } catch (e) {
      print('Error scanning connected devices: $e');
      return [];
    }
  }

  // Get supported IR protocols by the USB device
  Future<List<String>> getSupportedProtocols() async {
    try {
      final result = await _channel.invokeMethod('getSupportedProtocols');
      return List<String>.from(result['protocols'] ?? []);
    } catch (e) {
      print('Error getting supported protocols: $e');
      return ['NEC', 'RC5', 'RAW']; // Default protocols
    }
  }

  // Convert IR code to device-specific format
  Map<String, dynamic> _convertIrCodeForDevice(IrCode irCode, String deviceType) {
    switch (deviceType.toLowerCase()) {
      case 'arduino':
        return {
          'protocol': irCode.type,
          'frequency': irCode.freq,
          'data': irCode.pattern,
          'format': 'arduino_json'
        };
      case 'generic':
      default:
        return {
          'type': irCode.type,
          'freq': irCode.freq,
          'pattern': irCode.pattern,
          'format': 'standard'
        };
    }
  }

  // Get known device info by vendor/product ID
  Map<String, dynamic>? getKnownDeviceInfo(int vendorId, int productId) {
    try {
      return _knownDevices.firstWhere(
        (device) => device['vendorId'] == vendorId && device['productId'] == productId,
      );
    } catch (e) {
      return null;
    }
  }

  // Auto-detect device type and capabilities
  Future<String> detectDeviceType(UsbIrDevice device) async {
    final knownInfo = getKnownDeviceInfo(device.vendorId, device.productId);
    if (knownInfo != null) {
      return knownInfo['name'] as String;
    }

    // Try to detect based on communication
    try {
      final result = await _channel.invokeMethod('detectDeviceType', {
        'vendorId': device.vendorId,
        'productId': device.productId,
      });
      return result['deviceType'] as String? ?? 'Unknown USB IR Device';
    } catch (e) {
      return 'Unknown USB IR Device';
    }
  }
}
