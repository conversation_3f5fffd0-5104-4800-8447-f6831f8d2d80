com.example.remotedesigner.app-jetified-profileinstaller-1.4.0-0 C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\res
com.example.remotedesigner.app-lifecycle-viewmodel-2.7.0-1 C:\Users\<USER>\.gradle\caches\8.12\transforms\2ad53f7395904283a406130c8696bc04\transformed\lifecycle-viewmodel-2.7.0\res
com.example.remotedesigner.app-jetified-window-java-1.2.0-2 C:\Users\<USER>\.gradle\caches\8.12\transforms\2c87a9ed5028bb676b7be77db7955e90\transformed\jetified-window-java-1.2.0\res
com.example.remotedesigner.app-jetified-savedstate-1.2.1-3 C:\Users\<USER>\.gradle\caches\8.12\transforms\2d1b93c6fe96ab5bdd75270bb3c9c7ee\transformed\jetified-savedstate-1.2.1\res
com.example.remotedesigner.app-jetified-emoji2-1.2.0-4 C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\res
com.example.remotedesigner.app-fragment-1.7.1-5 C:\Users\<USER>\.gradle\caches\8.12\transforms\3c1099445a8641348914188dd3eeb162\transformed\fragment-1.7.1\res
com.example.remotedesigner.app-jetified-lifecycle-process-2.7.0-6 C:\Users\<USER>\.gradle\caches\8.12\transforms\4f7be8e0b7187bd3a834fd06c6e10470\transformed\jetified-lifecycle-process-2.7.0\res
com.example.remotedesigner.app-lifecycle-livedata-2.7.0-7 C:\Users\<USER>\.gradle\caches\8.12\transforms\60c32d40eaeb72d74b9a401a63802272\transformed\lifecycle-livedata-2.7.0\res
com.example.remotedesigner.app-core-1.13.1-8 C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\res
com.example.remotedesigner.app-jetified-core-viewtree-1.0.0-9 C:\Users\<USER>\.gradle\caches\8.12\transforms\63a48e0e279d39bafcbbe4ec4963a946\transformed\jetified-core-viewtree-1.0.0\res
com.example.remotedesigner.app-lifecycle-runtime-2.7.0-10 C:\Users\<USER>\.gradle\caches\8.12\transforms\67757b1b90f78be2a8e9f605893b1b7c\transformed\lifecycle-runtime-2.7.0\res
com.example.remotedesigner.app-jetified-startup-runtime-1.1.1-11 C:\Users\<USER>\.gradle\caches\8.12\transforms\71f3fea7346749b7f27594f95f34b0a2\transformed\jetified-startup-runtime-1.1.1\res
com.example.remotedesigner.app-jetified-window-1.2.0-12 C:\Users\<USER>\.gradle\caches\8.12\transforms\77a93e071b342b7344c87d70c4336d1c\transformed\jetified-window-1.2.0\res
com.example.remotedesigner.app-jetified-play-services-base-18.5.0-13 C:\Users\<USER>\.gradle\caches\8.12\transforms\7912ee62a7ffcad242aa1c9b5b1b3393\transformed\jetified-play-services-base-18.5.0\res
com.example.remotedesigner.app-exifinterface-1.4.1-14 C:\Users\<USER>\.gradle\caches\8.12\transforms\7d219b27b80352d4ec0cc114ad320c6f\transformed\exifinterface-1.4.1\res
com.example.remotedesigner.app-jetified-activity-1.10.1-15 C:\Users\<USER>\.gradle\caches\8.12\transforms\88ef443f815d0a5dd33aa331c1033668\transformed\jetified-activity-1.10.1\res
com.example.remotedesigner.app-jetified-core-1.0.0-16 C:\Users\<USER>\.gradle\caches\8.12\transforms\8cfd2a473567057bbc9daa9e207314e1\transformed\jetified-core-1.0.0\res
com.example.remotedesigner.app-jetified-lifecycle-livedata-core-ktx-2.7.0-17 C:\Users\<USER>\.gradle\caches\8.12\transforms\929dc30fdf4d67227a888eff5eed298a\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.remotedesigner.app-jetified-core-ktx-1.13.1-18 C:\Users\<USER>\.gradle\caches\8.12\transforms\98d58382faaf6381a8e63b418b440391\transformed\jetified-core-ktx-1.13.1\res
com.example.remotedesigner.app-jetified-emoji2-views-helper-1.2.0-19 C:\Users\<USER>\.gradle\caches\8.12\transforms\9bc135a5507b743174c0e273d8c579b4\transformed\jetified-emoji2-views-helper-1.2.0\res
com.example.remotedesigner.app-core-runtime-2.2.0-20 C:\Users\<USER>\.gradle\caches\8.12\transforms\a288bcf3f70957db18e7bc12df9f537e\transformed\core-runtime-2.2.0\res
com.example.remotedesigner.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-21 C:\Users\<USER>\.gradle\caches\8.12\transforms\a2ed842872067add618702b3da8bb9c5\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.remotedesigner.app-jetified-appcompat-resources-1.6.1-22 C:\Users\<USER>\.gradle\caches\8.12\transforms\a7e8e148e219468ab943c28c017ddb15\transformed\jetified-appcompat-resources-1.6.1\res
com.example.remotedesigner.app-jetified-tracing-1.2.0-23 C:\Users\<USER>\.gradle\caches\8.12\transforms\aa53b374b50c79695c7071879c3b28dd\transformed\jetified-tracing-1.2.0\res
com.example.remotedesigner.app-appcompat-1.6.1-24 C:\Users\<USER>\.gradle\caches\8.12\transforms\d1928fcfc97c48b7e6272f9cce0561a5\transformed\appcompat-1.6.1\res
com.example.remotedesigner.app-jetified-play-services-basement-18.4.0-25 C:\Users\<USER>\.gradle\caches\8.12\transforms\ec3bed3c7d477c70d95c50194b7855ae\transformed\jetified-play-services-basement-18.4.0\res
com.example.remotedesigner.app-jetified-annotation-experimental-1.4.0-26 C:\Users\<USER>\.gradle\caches\8.12\transforms\ee5fc285c354eec4877738a143fd8819\transformed\jetified-annotation-experimental-1.4.0\res
com.example.remotedesigner.app-lifecycle-livedata-core-2.7.0-27 C:\Users\<USER>\.gradle\caches\8.12\transforms\f75b8c602252404344e76eeefb8e3324\transformed\lifecycle-livedata-core-2.7.0\res
com.example.remotedesigner.app-debug-28 C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\debug\res
com.example.remotedesigner.app-main-29 C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\res
com.example.remotedesigner.app-pngs-30 C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\app\generated\res\pngs\debug
com.example.remotedesigner.app-resValues-31 C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\app\generated\res\resValues\debug
com.example.remotedesigner.app-packageDebugResources-32 C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.remotedesigner.app-packageDebugResources-33 C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.remotedesigner.app-debug-34 C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\app\intermediates\merged_res\debug\mergeDebugResources
com.example.remotedesigner.app-debug-35 C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\camera_android\intermediates\packaged_res\debug\packageDebugResources
com.example.remotedesigner.app-debug-36 C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\file_picker\intermediates\packaged_res\debug\packageDebugResources
com.example.remotedesigner.app-debug-37 C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\debug\packageDebugResources
com.example.remotedesigner.app-debug-38 C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\google_mlkit_commons\intermediates\packaged_res\debug\packageDebugResources
com.example.remotedesigner.app-debug-39 C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\google_mlkit_text_recognition\intermediates\packaged_res\debug\packageDebugResources
com.example.remotedesigner.app-debug-40 C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\packaged_res\debug\packageDebugResources
com.example.remotedesigner.app-debug-41 C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\path_provider_android\intermediates\packaged_res\debug\packageDebugResources
com.example.remotedesigner.app-debug-42 C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\permission_handler_android\intermediates\packaged_res\debug\packageDebugResources
com.example.remotedesigner.app-debug-43 C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\packaged_res\debug\packageDebugResources
