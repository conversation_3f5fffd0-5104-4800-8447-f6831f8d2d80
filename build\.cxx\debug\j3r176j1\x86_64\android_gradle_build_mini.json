{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\remote_designer\\build\\.cxx\\debug\\j3r176j1\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\remote_designer\\build\\.cxx\\debug\\j3r176j1\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}