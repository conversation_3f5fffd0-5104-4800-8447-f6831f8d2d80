package com.example.remotedesigner

import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.hardware.ConsumerIrManager
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbDeviceConnection
import android.hardware.usb.UsbManager
import android.hardware.usb.UsbConstants
import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val IR_CHANNEL = "remotedesigner/ir"
    private val IMAGE_ANALYSIS_CHANNEL = "remotedesigner/image_analysis"
    private val USB_IR_CHANNEL = "remotedesigner/usb_ir"

    private lateinit var irManager: ConsumerIrManager
    private lateinit var usbManager: UsbManager
    private var usbDevice: UsbDevice? = null
    private var usbConnection: UsbDeviceConnection? = null

    private val ACTION_USB_PERMISSION = "com.example.remotedesigner.USB_PERMISSION"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Initialize IR manager
        irManager = getSystemService(Context.CONSUMER_IR_SERVICE) as ConsumerIrManager

        // Initialize USB manager
        usbManager = getSystemService(Context.USB_SERVICE) as UsbManager

        // Register USB permission receiver
        val filter = IntentFilter(ACTION_USB_PERMISSION)
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(usbReceiver, filter, Context.RECEIVER_NOT_EXPORTED)
        } else {
            registerReceiver(usbReceiver, filter)
        }

        // Set up IR method channel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, IR_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "hasIrTransmitter" -> {
                    try {
                        val hasIr = irManager.hasIrEmitter()
                        print("Built-in IR capability check: $hasIr")

                        // التحقق من إصدار Android لدعم ConsumerIrManager
                        val sdkVersion = android.os.Build.VERSION.SDK_INT
                        val hasIrManager = sdkVersion >= android.os.Build.VERSION_CODES.KITKAT
                        print("Android SDK Version: $sdkVersion, IR Manager support: $hasIrManager")

                        // إذا كان الجهاز يدعم IR لكن لا يظهر في القائمة، قد يكون هناك مشكلة في الإعدادات
                        if (!hasIr && hasIrManager) {
                            print("Device supports IR manager but no emitter detected. This may indicate a hardware or configuration issue.")
                        }

                        result.success(hasIr)
                    } catch (e: Exception) {
                        print("Error checking IR capability: ${e.message}")
                        result.error("IR_ERROR", "Failed to check IR capability: ${e.message}", null)
                    }
                }
                "transmitIr" -> {
                    val frequency = call.argument<Int>("frequency") ?: 38000
                    val pattern = call.argument<List<Int>>("pattern") ?: emptyList()

                    try {
                        if (irManager.hasIrEmitter()) {
                            irManager.transmit(frequency, pattern.toIntArray())
                            result.success(true)
                        } else {
                            result.success(false)
                        }
                    } catch (e: Exception) {
                        result.error("IR_ERROR", "Failed to transmit IR signal: ${e.message}", null)
                    }
                }
                "learnIrSignal" -> {
                    // IR learning is not supported by standard Android API
                    // This would require custom hardware or USB dongles
                    result.success(null)
                }
                "checkUsbIrDongle" -> {
                    val hasUsbDevice = checkForUsbIrDevices()
                    result.success(hasUsbDevice)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }

        // Set up USB IR method channel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, USB_IR_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "getConnectedDevices" -> {
                    val devices = getConnectedUsbIrDevices()
                    result.success(mapOf("devices" to devices))
                }
                "requestPermission" -> {
                    val vendorId = call.argument<Int>("vendorId") ?: 0
                    val productId = call.argument<Int>("productId") ?: 0
                    requestUsbPermission(vendorId, productId, result)
                }
                "connectToDevice" -> {
                    val vendorId = call.argument<Int>("vendorId") ?: 0
                    val productId = call.argument<Int>("productId") ?: 0
                    val success = connectToUsbDevice(vendorId, productId)
                    result.success(success)
                }
                "disconnectFromDevice" -> {
                    val success = disconnectUsbDevice()
                    result.success(success)
                }
                "transmitIrViaUsb" -> {
                    val frequency = call.argument<Int>("frequency") ?: 38000
                    val pattern = call.argument<List<Int>>("pattern") ?: emptyList()
                    val type = call.argument<String>("type") ?: "RAW"
                    val success = transmitIrViaUsb(frequency, pattern, type)
                    result.success(success)
                }
                "learnIrViaUsb" -> {
                    val timeoutSeconds = call.argument<Int>("timeoutSeconds") ?: 10
                    learnIrViaUsb(timeoutSeconds, result)
                }
                "isDeviceConnected" -> {
                    result.success(usbConnection != null && usbDevice != null)
                }
                "testDeviceConnection" -> {
                    val success = testUsbConnection()
                    result.success(success)
                }
                "scanAllUsbDevices" -> {
                    val devices = scanAllConnectedDevices()
                    result.success(mapOf("devices" to devices))
                }
                else -> {
                    result.notImplemented()
                }
            }
        }

        // Set up image analysis method channel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, IMAGE_ANALYSIS_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "analyzeImage" -> {
                    // For MVP, we'll return empty results and let Flutter handle OCR
                    // In a full implementation, this would use OpenCV for contour detection
                    val candidates = emptyList<Map<String, Any>>()
                    result.success(mapOf("candidates" to candidates))
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    // USB permission receiver
    private val usbReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                ACTION_USB_PERMISSION -> {
                    synchronized(this) {
                        val device: UsbDevice? = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                            intent.getParcelableExtra(UsbManager.EXTRA_DEVICE, UsbDevice::class.java)
                        } else {
                            @Suppress("DEPRECATION")
                            intent.getParcelableExtra(UsbManager.EXTRA_DEVICE)
                        }
                        if (intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)) {
                            device?.let {
                                // Permission granted, can now communicate with device
                                usbDevice = it
                                usbConnection = usbManager.openDevice(it)
                            }
                        }
                    }
                }
            }
        }
    }

    // مسح جميع الأجهزة المتصلة للبحث عن أجهزة IR
    private fun scanAllConnectedDevices(): List<Map<String, Any>> {
        val devices = mutableListOf<Map<String, Any>>()
        val deviceList = usbManager.deviceList

        print("Scanning all connected USB devices...")

        for (device in deviceList.values) {
            print("Found device: ${device.deviceName}")
            print("  Vendor: 0x${device.vendorId.toString(16)}, Product: 0x${device.productId.toString(16)}")

            // محاولة فتح الجهاز مباشرة للتحقق مما إذا كان جهاز IR
            if (usbManager.hasPermission(device)) {
                val connection = usbManager.openDevice(device)
                if (connection != null) {
                    try {
                        // محاولة إرسال أمر بسيط للتحقق من الجهاز
                        val testCommand = byteArrayOf(0x00, 0x00)
                        val interfaceCount = device.interfaceCount
                        if (interfaceCount > 0) {
                            val endpointCount = device.getInterface(0).endpointCount
                            if (endpointCount > 0) {
                                val endpoint = device.getInterface(0).getEndpoint(0)
                                if (endpoint.direction == UsbConstants.USB_DIR_OUT) {
                                    val result = connection.bulkTransfer(endpoint, testCommand, testCommand.size, 500)
                                    if (result >= 0) {
                                        print("  Device responded to test - likely IR device")
                                        devices.add(mapOf(
                                            "name" to (device.productName ?: "Unknown Device"),
                                            "vendorId" to device.vendorId,
                                            "productId" to device.productId,
                                            "devicePath" to device.deviceName,
                                            "canTransmit" to true,
                                            "canReceive" to true,
                                            "isTested" to true
                                        ))
                                    }
                                }
                            }
                        }
                    } catch (e: Exception) {
                        print("  Error testing device: ${e.message}")
                    } finally {
                        connection.close()
                    }
                }
            }
        }

        print("Found ${devices.size} potential IR devices")
        return devices
    }

    // Check for USB IR devices
    private fun checkForUsbIrDevices(): Boolean {
        val deviceList = usbManager.deviceList
        val hasIrDevices = deviceList.values.any { isIrDevice(it) }
        
        // Debug output
        print("USB IR Device Check:")
        print("Found ${deviceList.size} USB devices")
        deviceList.values.forEach { device ->
            print("Device: ${device.deviceName}, Vendor: 0x${device.vendorId.toString(16)}, Product: 0x${device.productId.toString(16)}, Is IR: ${isIrDevice(device)}")
        }
        print("Has IR devices: $hasIrDevices")
        
        return hasIrDevices
    }

    // Get connected USB IR devices
    private fun getConnectedUsbIrDevices(): List<Map<String, Any>> {
        val devices = mutableListOf<Map<String, Any>>()
        val deviceList = usbManager.deviceList
        
        print("Getting connected USB IR devices:")
        print("Found ${deviceList.size} USB devices")

        for (device in deviceList.values) {
            if (isIrDevice(device)) {
                print("Found IR device: ${device.productName}")
                devices.add(mapOf(
                    "name" to (device.productName ?: "Unknown USB IR Device"),
                    "vendorId" to device.vendorId,
                    "productId" to device.productId,
                    "devicePath" to device.deviceName,
                    "canTransmit" to true,
                    "canReceive" to true
                ))
            }
        }
        
        print("Returning ${devices.size} IR devices")
        return devices
    }

    // Check if device is an IR device (based on known vendor/product IDs or generic USB characteristics)
    private fun isIrDevice(device: UsbDevice): Boolean {
        val knownIrDevices = listOf(
            Pair(0x1234, 0x5678), // Generic USB IR
            Pair(0x2341, 0x0043), // Arduino Uno
            Pair(0x067b, 0x2303), // IrDA USB Adapter
            Pair(0x2341, 0x0010), // Arduino Mega
            Pair(0x1a86, 0x7523), // CH340 Serial Adapter (sometimes used with Arduino)
            Pair(0x10c4, 0xea60), // CP2102 USB to UART Bridge (sometimes used with Arduino)
            Pair(0x04d8, 0x00df), // Microchip (PIC) devices
            Pair(0x16c0, 0x0483), // VOTI shared PID
            Pair(0x16c0, 0x05df), // VOTI shared PID
            Pair(0x239a, 0x0001), // Adafruit Industries PID
        )

        val isKnownIrDevice = knownIrDevices.any {
            it.first == device.vendorId && it.second == device.productId
        }
        
        val isNamedIrDevice = device.productName?.contains("IR", ignoreCase = true) == true ||
                             device.productName?.contains("infrared", ignoreCase = true) == true ||
                             device.productName?.contains("remote", ignoreCase = true) == true

        // Generic check: Devices with bulk endpoints are often IR/serial adapters
        val hasBulkEndpoints = if (device.interfaceCount > 0) {
            val interface0 = device.getInterface(0)
            var bulkCount = 0
            for (i in 0 until interface0.endpointCount) {
                val endpoint = interface0.getEndpoint(i)
                if (endpoint.type == UsbConstants.USB_ENDPOINT_XFER_BULK) {
                    bulkCount++
                }
            }
            bulkCount >= 2 // Typically IN and OUT bulk endpoints for bidirectional IR
        } else {
            false
        }
        
        val result = isKnownIrDevice || isNamedIrDevice || hasBulkEndpoints
        
        // Debug output
        print("Checking device: ${device.deviceName}")
        print("  Vendor ID: 0x${device.vendorId.toString(16).padStart(4, '0')}, Product ID: 0x${device.productId.toString(16).padStart(4, '0')}")
        print("  Product Name: ${device.productName}")
        print("  Interfaces: ${device.interfaceCount}, Has Bulk Endpoints: $hasBulkEndpoints")
        print("  Is Known IR Device: $isKnownIrDevice")
        print("  Is Named IR Device: $isNamedIrDevice")
        print("  Is IR Device: $result")
        
        return result
    }

    // Request USB permission
    private fun requestUsbPermission(vendorId: Int, productId: Int, result: MethodChannel.Result) {
        val deviceList = usbManager.deviceList
        val device = deviceList.values.find {
            it.vendorId == vendorId && it.productId == productId
        }

        if (device == null) {
            print("USB device not found")
            result.success(false)
            return
        }

        if (usbManager.hasPermission(device)) {
            print("USB permission already granted")
            usbDevice = device
            usbConnection = usbManager.openDevice(device)
            result.success(usbConnection != null)
            return
        }

        try {
            val permissionIntent = PendingIntent.getBroadcast(
                this, 0, Intent(ACTION_USB_PERMISSION), PendingIntent.FLAG_IMMUTABLE
            )
            usbManager.requestPermission(device, permissionIntent)
            print("USB permission requested for device: ${device.deviceName}")
            result.success(true)
        } catch (e: Exception) {
            print("Error requesting USB permission: ${e.message}")
            result.error("USB_PERMISSION_ERROR", "Failed to request permission: ${e.message}", null)
        }
    }

    // Connect to USB device
    private fun connectToUsbDevice(vendorId: Int, productId: Int): Boolean {
        val deviceList = usbManager.deviceList
        val device = deviceList.values.find {
            it.vendorId == vendorId && it.productId == productId
        }

        return if (device != null) {
            if (!usbManager.hasPermission(device)) {
                print("Permission not granted for device: ${device.deviceName}")
                return false
            }

            try {
                usbDevice = device
                usbConnection = usbManager.openDevice(device)

                if (usbConnection != null) {
                    print("Successfully connected to USB device: ${device.deviceName}")
                    print("  Vendor: 0x${device.vendorId.toString(16)}, Product: 0x${device.productId.toString(16)}")
                    true
                } else {
                    print("Failed to open connection to device: ${device.deviceName}")
                    false
                }
            } catch (e: Exception) {
                print("Error connecting to USB device: ${e.message}")
                false
            }
        } else {
            print("USB device not found")
            false
        }
    }

    // Disconnect USB device
    private fun disconnectUsbDevice(): Boolean {
        return try {
            usbConnection?.close()
            usbConnection = null
            usbDevice = null
            true
        } catch (e: Exception) {
            false
        }
    }

    // Transmit IR via USB device
    private fun transmitIrViaUsb(frequency: Int, pattern: List<Int>, type: String): Boolean {
        return try {
            if (usbConnection == null || usbDevice == null) {
                print("USB connection not established")
                return false
            }

            // التحقق من وجود الواجهة ونقاط النهاية
            val interfaceCount = usbDevice!!.interfaceCount
            if (interfaceCount == 0) {
                print("Device has no interfaces")
                return false
            }

            val usbInterface = usbDevice!!.getInterface(0)
            val endpointCount = usbInterface.endpointCount
            if (endpointCount == 0) {
                print("Interface has no endpoints")
                return false
            }

            // البحث عن نقطة نهاية الإرسال (OUT endpoint)
            val outEndpoint = usbInterface.getEndpoint(0)
            if (outEndpoint.direction != UsbConstants.USB_DIR_OUT) {
                print("Endpoint 0 is not an OUT endpoint")
                return false
            }

            // بناء أمر IR
            val command = buildIrCommand(frequency, pattern, type)
            print("Built IR command with ${command.size} bytes")

            // إرسال الأمر
            val result = usbConnection!!.bulkTransfer(outEndpoint, command, command.size, 1000)
            if (result >= 0) {
                print("Successfully transmitted IR command via USB, bytes sent: $result")
                true
            } else {
                print("USB transmission failed with result: $result")
                false
            }
        } catch (e: Exception) {
            print("Error transmitting IR via USB: ${e.message}")
            false
        }
    }

    // Learn IR via USB device
    private fun learnIrViaUsb(timeoutSeconds: Int, result: MethodChannel.Result) {
        try {
            if (usbConnection == null || usbDevice == null) {
                print("USB connection not established")
                result.success(null)
                return
            }

            // التحقق من وجود الواجهة ونقاط النهاية
            val interfaceCount = usbDevice!!.interfaceCount
            if (interfaceCount == 0) {
                print("Device has no interfaces")
                result.success(null)
                return
            }

            val usbInterface = usbDevice!!.getInterface(0)
            val endpointCount = usbInterface.endpointCount
            if (endpointCount == 0) {
                print("Interface has no endpoints")
                result.success(null)
                return
            }

            // البحث عن نقطة نهاية الاستقبال (IN endpoint)
            var inEndpoint = usbInterface.getEndpoint(0)
            if (inEndpoint.direction != UsbConstants.USB_DIR_IN) {
                // إذا لم تكن نقطة النهاية الأولى للدخول، حاول البحث عن نقطة نهاية أخرى
                var foundInEndpoint = false
                for (i in 0 until endpointCount) {
                    val endpoint = usbInterface.getEndpoint(i)
                    if (endpoint.direction == UsbConstants.USB_DIR_IN) {
                        inEndpoint = endpoint
                        foundInEndpoint = true
                        break
                    }
                }

                if (!foundInEndpoint) {
                    print("No IN endpoint found for IR learning")
                    result.success(null)
                    return
                }
            }

            print("Starting IR learning with timeout: $timeoutSeconds seconds")

            // إرسال أمر بدء التعلم
            val startCommand = byteArrayOf(0x03, 0x00) // أمر بدء التعلم
            val startResult = usbConnection!!.bulkTransfer(
                usbInterface.getEndpoint(0),
                startCommand,
                startCommand.size,
                1000
            )

            if (startResult < 0) {
                print("Failed to send start learning command")
                result.success(null)
                return
            }

            // في تطبيق حقيقي، هنا سنقوم بقراءة البيانات من نقطة النهاية IN
            // ولنفترض أننا نحصل على نمط IR
            print("Simulating IR learning...")

            // تأخير لفترة قصيرة لمحاكاة عملية التعلم
            Thread.sleep(timeoutSeconds * 1000L)

            // في تطبيق حقيقي، هنا سنقوم بتحليل البيانات المستلمة
            // ولنفترض أننا تلقينا نمط IR
            val learnedPattern = listOf(9000, 4500, 560, 560, 560, 1690) // نمط تجريبي

            val learnedCode = mapOf(
                "success" to true,
                "type" to "NEC",
                "frequency" to 38000,
                "pattern" to learnedPattern
            )

            print("Learned IR pattern: ${learnedPattern.joinToString(", ")}")
            result.success(learnedCode)

        } catch (e: Exception) {
            print("Error learning IR via USB: ${e.message}")
            result.error("IR_LEARNING_ERROR", "Failed to learn IR: ${e.message}", null)
        }
    }

    // Test USB connection
    private fun testUsbConnection(): Boolean {
        return try {
            if (usbConnection == null || usbDevice == null) {
                print("USB connection not established")
                return false
            }

            // التحقق من وجود الواجهة ونقاط النهاية
            val interfaceCount = usbDevice!!.interfaceCount
            if (interfaceCount == 0) {
                print("Device has no interfaces")
                return false
            }

            val usbInterface = usbDevice!!.getInterface(0)
            val endpointCount = usbInterface.endpointCount
            if (endpointCount == 0) {
                print("Interface has no endpoints")
                return false
            }

            // البحث عن نقطة نهاية الإرسال (OUT endpoint)
            val outEndpoint = usbInterface.getEndpoint(0)
            if (outEndpoint.direction != UsbConstants.USB_DIR_OUT) {
                print("Endpoint 0 is not an OUT endpoint")
                return false
            }

            // إرسال أمر اختبار بسيط
            val testCommand = byteArrayOf(0x01, 0x00) // أمر ping بسيط
            print("Sending test command: ${testCommand.joinToString(", ")}")

            val result = usbConnection!!.bulkTransfer(outEndpoint, testCommand, testCommand.size, 1000)
            if (result >= 0) {
                print("USB connection test successful, bytes transferred: $result")
                true
            } else {
                print("USB connection test failed with result: $result")
                false
            }
        } catch (e: Exception) {
            print("Error testing USB connection: ${e.message}")
            false
        }
    }

    // التحقق من إصدار Android ودعم الميزات المناسبة
    private fun checkAndroidVersionFeatures(): Map<String, Boolean> {
        val sdkVersion = android.os.Build.VERSION.SDK_INT
        val features = mutableMapOf<String, Boolean>()

        // دعم ConsumerIrManager
        features["hasIrManager"] = sdkVersion >= android.os.Build.VERSION_CODES.KITKAT

        // دعم UsbManager
        features["hasUsbManager"] = true // مدعوم من API 12

        // دعم UsbDeviceConnection للإرسال المتزامن
        features["hasSyncUsb"] = sdkVersion >= android.os.Build.VERSION_CODES.LOLLIPOP

        // دعم UsbRequest للإرسال غير المتزامن
        features["hasAsyncUsb"] = sdkVersion >= android.os.Build.VERSION_CODES.HONEYCOMB

        print("Android SDK Version: $sdkVersion")
        print("Available features: $features")

        return features
    }

    // Build IR command for USB device
    private fun buildIrCommand(frequency: Int, pattern: List<Int>, type: String): ByteArray {
        val command = mutableListOf<Byte>()

        // إضافة رأس الأمر
        command.add(0x02) // أمر إرسال IR

        // إضافة نوع البروتوكول
        val protocolType = when (type.uppercase()) {
            "NEC" -> 0x01
            "RC5" -> 0x02
            "RC6" -> 0x03
            "SONY" -> 0x04
            "SAMSUNG" -> 0x05
            "RAW" -> 0x00
            else -> 0x00 // RAW كإفتراضي
        }
        command.add(protocolType.toByte())

        // إضافة التردد (2 bytes, little endian)
        command.add((frequency and 0xFF).toByte())
        command.add(((frequency shr 8) and 0xFF).toByte())

        // إضافة طول النمط (2 bytes, little endian)
        command.add((pattern.size and 0xFF).toByte())
        command.add(((pattern.size shr 8) and 0xFF).toByte())

        // إضافة بيانات النمط
        for (value in pattern) {
            command.add((value and 0xFF).toByte())
            command.add(((value shr 8) and 0xFF).toByte())
        }

        print("Built IR command for protocol: $type, frequency: $frequency Hz, pattern length: ${pattern.size}")

        return command.toByteArray()
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            unregisterReceiver(usbReceiver)
        } catch (e: Exception) {
            // Receiver might not be registered
        }
        disconnectUsbDevice()
    }
}
