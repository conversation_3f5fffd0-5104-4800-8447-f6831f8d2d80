import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/services.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import '../models/button.dart';

class ButtonCandidate {
  final BoundingBox bbox;
  final String? detectedText;
  final double confidence;

  ButtonCandidate({
    required this.bbox,
    this.detectedText,
    required this.confidence,
  });
}

class ImageAnalysisService {
  static const MethodChannel _channel = MethodChannel('remotedesigner/image_analysis');
  final TextRecognizer _textRecognizer = TextRecognizer();

  // Analyze image to detect button candidates
  Future<List<ButtonCandidate>> analyzeImage(File imageFile) async {
    try {
      // First, try to use native OpenCV analysis (if available)
      final candidates = await _analyzeWithOpenCV(imageFile);
      if (candidates.isNotEmpty) {
        return candidates;
      }
    } catch (e) {
      print('OpenCV analysis failed, falling back to basic analysis: $e');
    }

    // Fallback to basic analysis with OCR
    return await _analyzeWithOCR(imageFile);
  }

  // Native OpenCV analysis (requires platform channel implementation)
  Future<List<ButtonCandidate>> _analyzeWithOpenCV(File imageFile) async {
    try {
      final imageBytes = await imageFile.readAsBytes();
      final result = await _channel.invokeMethod('analyzeImage', {
        'imageBytes': imageBytes,
      });

      final List<dynamic> candidatesData = result['candidates'] ?? [];
      return candidatesData.map((data) {
        return ButtonCandidate(
          bbox: BoundingBox(
            x: (data['x'] as num).toDouble(),
            y: (data['y'] as num).toDouble(),
            w: (data['w'] as num).toDouble(),
            h: (data['h'] as num).toDouble(),
          ),
          detectedText: data['text'] as String?,
          confidence: (data['confidence'] as num).toDouble(),
        );
      }).toList();
    } catch (e) {
      print('Native OpenCV analysis error: $e');
      return [];
    }
  }

  // Fallback OCR-based analysis
  Future<List<ButtonCandidate>> _analyzeWithOCR(File imageFile) async {
    try {
      final inputImage = InputImage.fromFile(imageFile);
      final recognizedText = await _textRecognizer.processImage(inputImage);
      
      final List<ButtonCandidate> candidates = [];
      
      for (final textBlock in recognizedText.blocks) {
        for (final textLine in textBlock.lines) {
          // Filter text that looks like button labels
          if (_isLikelyButtonText(textLine.text)) {
            final rect = textLine.boundingBox;
            candidates.add(ButtonCandidate(
              bbox: BoundingBox(
                x: rect.left.toDouble(),
                y: rect.top.toDouble(),
                w: rect.width.toDouble(),
                h: rect.height.toDouble(),
              ),
              detectedText: textLine.text,
              confidence: textLine.confidence ?? 0.5,
            ));
          }
        }
      }

      return candidates;
    } catch (e) {
      print('OCR analysis error: $e');
      return [];
    }
  }

  // Check if text looks like a button label
  bool _isLikelyButtonText(String text) {
    final cleanText = text.trim().toLowerCase();
    
    // Common button texts
    final commonButtons = [
      'power', 'vol', 'volume', 'ch', 'channel', 'mute', 'menu', 'ok', 'enter',
      'up', 'down', 'left', 'right', 'back', 'home', 'play', 'pause', 'stop',
      'record', 'rewind', 'forward', 'next', 'prev', 'source', 'input',
      '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '+', '-'
    ];

    // Check if text matches common button patterns
    for (final buttonText in commonButtons) {
      if (cleanText.contains(buttonText)) {
        return true;
      }
    }

    // Check for single characters or short text (likely buttons)
    if (cleanText.length <= 3 && cleanText.isNotEmpty) {
      return true;
    }

    return false;
  }

  // Convert candidates to Button objects
  List<Button> candidatesToButtons(List<ButtonCandidate> candidates) {
    return candidates.asMap().entries.map((entry) {
      final index = entry.key;
      final candidate = entry.value;
      
      return Button(
        id: 'detected_${index}_${DateTime.now().millisecondsSinceEpoch}',
        label: candidate.detectedText ?? 'Button ${index + 1}',
        bbox: candidate.bbox,
        style: Style(
          bgColor: _getButtonColor(candidate.detectedText),
          textColor: '#ffffff',
        ),
      );
    }).toList();
  }

  // Get appropriate color for button based on detected text
  String _getButtonColor(String? text) {
    if (text == null) return '#2196F3';
    
    final cleanText = text.toLowerCase();
    
    if (cleanText.contains('power')) return '#ff0000';
    if (cleanText.contains('vol')) return '#4CAF50';
    if (cleanText.contains('ch')) return '#2196F3';
    if (cleanText.contains('mute')) return '#FF9800';
    if (cleanText.contains('menu')) return '#9C27B0';
    if (cleanText.contains('ok') || cleanText.contains('enter')) return '#4CAF50';
    
    return '#607D8B'; // Default gray
  }

  void dispose() {
    _textRecognizer.close();
  }
}
