1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.remotedesigner"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:10:5-67
15-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:10:22-64
16    <!-- Permissions for IR transmission -->
17    <uses-permission android:name="android.permission.TRANSMIT_IR" /> <!-- Permissions for camera and storage -->
17-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:3:5-70
17-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:3:22-67
18    <uses-permission android:name="android.permission.CAMERA" />
18-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:6:5-65
18-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:6:22-62
19    <uses-permission
19-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:7:5-80
20        android:name="android.permission.READ_EXTERNAL_STORAGE"
20-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:7:22-77
21        android:maxSdkVersion="32" />
21-->[:file_picker] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-35
22    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
22-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:8:5-81
22-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:8:22-78
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" /> <!-- USB Host permission for IR dongles -->
23-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:9:5-76
23-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:9:22-73
24    <uses-permission android:name="android.permission.USB_PERMISSION" />
24-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:13:5-73
24-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:13:22-70
25
26    <uses-feature
26-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:14:5-87
27        android:name="android.hardware.usb.host"
27-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:14:19-59
28        android:required="false" /> <!-- IR hardware feature (optional) -->
28-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:14:60-84
29    <uses-feature
29-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:17:5-89
30        android:name="android.hardware.consumerir"
30-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:17:19-61
31        android:required="false" />
31-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:17:62-86
32    <!--
33 Required to query activities that can process text, see:
34         https://developer.android.com/training/package-visibility and
35         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
36
37         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
38    -->
39    <queries>
39-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:56:5-61:15
40        <intent>
40-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:57:9-60:18
41            <action android:name="android.intent.action.PROCESS_TEXT" />
41-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:58:13-72
41-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:58:21-70
42
43            <data android:mimeType="text/plain" />
43-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:59:13-50
43-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:59:19-48
44        </intent>
45        <intent>
45-->[:file_picker] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-16:18
46            <action android:name="android.intent.action.GET_CONTENT" />
46-->[:file_picker] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-72
46-->[:file_picker] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:21-69
47
48            <data android:mimeType="*/*" />
48-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:59:13-50
48-->C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\AndroidManifest.xml:59:19-48
49        </intent>
50    </queries>
51
52    <uses-permission android:name="android.permission.RECORD_AUDIO" />
52-->[:camera_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-71
52-->[:camera_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-68
53
54    <permission
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
55        android:name="com.example.remotedesigner.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
56        android:protectionLevel="signature" />
56-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
57
58    <uses-permission android:name="com.example.remotedesigner.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
58-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
58-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
59    <!-- <uses-sdk android:minSdkVersion="14"/> -->
60    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
60-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
60-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
61
62    <application
63        android:name="android.app.Application"
64        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
64-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62c48e5d57466ca58583eec4682002ab\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
65        android:debuggable="true"
66        android:extractNativeLibs="false"
67        android:icon="@mipmap/ic_launcher"
68        android:label="Remote Designer" >
69        <activity
70            android:name="com.example.remotedesigner.MainActivity"
71            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
72            android:exported="true"
73            android:hardwareAccelerated="true"
74            android:launchMode="singleTop"
75            android:taskAffinity=""
76            android:theme="@style/LaunchTheme"
77            android:windowSoftInputMode="adjustResize" >
78
79            <!--
80                 Specifies an Android theme to apply to this Activity as soon as
81                 the Android process has started. This theme is visible to the user
82                 while the Flutter UI initializes. After that, this theme continues
83                 to determine the Window background behind the Flutter UI.
84            -->
85            <meta-data
86                android:name="io.flutter.embedding.android.NormalTheme"
87                android:resource="@style/NormalTheme" />
88
89            <intent-filter>
90                <action android:name="android.intent.action.MAIN" />
91
92                <category android:name="android.intent.category.LAUNCHER" />
93            </intent-filter>
94        </activity>
95        <!--
96             Don't delete the meta-data below.
97             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
98        -->
99        <meta-data
100            android:name="flutterEmbedding"
101            android:value="2" />
102        <!--
103           Declares a provider which allows us to store files to share in
104           '.../caches/share_plus' and grant the receiving action access
105        -->
106        <provider
106-->[:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
107            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
107-->[:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
108            android:authorities="com.example.remotedesigner.flutter.share_provider"
108-->[:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
109            android:exported="false"
109-->[:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
110            android:grantUriPermissions="true" >
110-->[:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
111            <meta-data
111-->[:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
112                android:name="android.support.FILE_PROVIDER_PATHS"
112-->[:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
113                android:resource="@xml/flutter_share_file_paths" />
113-->[:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
114        </provider>
115        <!--
116           This manifest declared broadcast receiver allows us to use an explicit
117           Intent when creating a PendingItent to be informed of the user's choice
118        -->
119        <receiver
119-->[:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
120            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
120-->[:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
121            android:exported="false" >
121-->[:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
122            <intent-filter>
122-->[:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
123                <action android:name="EXTRA_CHOSEN_COMPONENT" />
123-->[:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
123-->[:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
124            </intent-filter>
125        </receiver>
126
127        <provider
127-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
128            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
128-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
129            android:authorities="com.example.remotedesigner.flutter.image_provider"
129-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
130            android:exported="false"
130-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
131            android:grantUriPermissions="true" >
131-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
132            <meta-data
132-->[:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
133                android:name="android.support.FILE_PROVIDER_PATHS"
133-->[:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
134                android:resource="@xml/flutter_image_picker_file_paths" />
134-->[:share_plus] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
135        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
136        <service
136-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
137            android:name="com.google.android.gms.metadata.ModuleDependencies"
137-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
138            android:enabled="false"
138-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
139            android:exported="false" >
139-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
140            <intent-filter>
140-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
141                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
141-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
141-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
142            </intent-filter>
143
144            <meta-data
144-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
145                android:name="photopicker_activity:0:required"
145-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
146                android:value="" />
146-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
147        </service>
148
149        <uses-library
149-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77a93e071b342b7344c87d70c4336d1c\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
150            android:name="androidx.window.extensions"
150-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77a93e071b342b7344c87d70c4336d1c\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
151            android:required="false" />
151-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77a93e071b342b7344c87d70c4336d1c\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
152        <uses-library
152-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77a93e071b342b7344c87d70c4336d1c\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
153            android:name="androidx.window.sidecar"
153-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77a93e071b342b7344c87d70c4336d1c\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
154            android:required="false" />
154-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77a93e071b342b7344c87d70c4336d1c\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
155
156        <service
156-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c2cce33d811c356b28eb01e2ed28f4e\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:9:9-15:19
157            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
157-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c2cce33d811c356b28eb01e2ed28f4e\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:10:13-91
158            android:directBootAware="true"
158-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:17:13-43
159            android:exported="false" >
159-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c2cce33d811c356b28eb01e2ed28f4e\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:11:13-37
160            <meta-data
160-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c2cce33d811c356b28eb01e2ed28f4e\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:12:13-14:85
161                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
161-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c2cce33d811c356b28eb01e2ed28f4e\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:13:17-114
162                android:value="com.google.firebase.components.ComponentRegistrar" />
162-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c2cce33d811c356b28eb01e2ed28f4e\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:14:17-82
163            <meta-data
163-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\312c2df5dd04ad542212dc294df5d7cd\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
164                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
164-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\312c2df5dd04ad542212dc294df5d7cd\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
165                android:value="com.google.firebase.components.ComponentRegistrar" />
165-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\312c2df5dd04ad542212dc294df5d7cd\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
166            <meta-data
166-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:20:13-22:85
167                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
167-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:21:17-120
168                android:value="com.google.firebase.components.ComponentRegistrar" />
168-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:22:17-82
169        </service>
170
171        <provider
171-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:9:9-13:38
172            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
172-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:10:13-78
173            android:authorities="com.example.remotedesigner.mlkitinitprovider"
173-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:11:13-69
174            android:exported="false"
174-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:12:13-37
175            android:initOrder="99" />
175-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b629ac78be7afc069d713de10943ce05\transformed\jetified-common-18.11.0\AndroidManifest.xml:13:13-35
176
177        <activity
177-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7912ee62a7ffcad242aa1c9b5b1b3393\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
178            android:name="com.google.android.gms.common.api.GoogleApiActivity"
178-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7912ee62a7ffcad242aa1c9b5b1b3393\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
179            android:exported="false"
179-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7912ee62a7ffcad242aa1c9b5b1b3393\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
180            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
180-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7912ee62a7ffcad242aa1c9b5b1b3393\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
181
182        <meta-data
182-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec3bed3c7d477c70d95c50194b7855ae\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
183            android:name="com.google.android.gms.version"
183-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec3bed3c7d477c70d95c50194b7855ae\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
184            android:value="@integer/google_play_services_version" />
184-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec3bed3c7d477c70d95c50194b7855ae\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
185
186        <provider
186-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
187            android:name="androidx.startup.InitializationProvider"
187-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
188            android:authorities="com.example.remotedesigner.androidx-startup"
188-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
189            android:exported="false" >
189-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
190            <meta-data
190-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
191                android:name="androidx.emoji2.text.EmojiCompatInitializer"
191-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
192                android:value="androidx.startup" />
192-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30b429c6b4827f4f9b295d55a97af975\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
193            <meta-data
193-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f7be8e0b7187bd3a834fd06c6e10470\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
194                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
194-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f7be8e0b7187bd3a834fd06c6e10470\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
195                android:value="androidx.startup" />
195-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f7be8e0b7187bd3a834fd06c6e10470\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
196            <meta-data
196-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
197                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
198                android:value="androidx.startup" />
198-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
199        </provider>
200
201        <receiver
201-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
202            android:name="androidx.profileinstaller.ProfileInstallReceiver"
202-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
203            android:directBootAware="false"
203-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
204            android:enabled="true"
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
205            android:exported="true"
205-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
206            android:permission="android.permission.DUMP" >
206-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
207            <intent-filter>
207-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
208                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
208-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
208-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
209            </intent-filter>
210            <intent-filter>
210-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
211                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
211-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
211-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
212            </intent-filter>
213            <intent-filter>
213-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
214                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
214-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
214-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
215            </intent-filter>
216            <intent-filter>
216-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
217                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
217-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
217-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0665b9101c8b8ede27fdb83d0029f178\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
218            </intent-filter>
219        </receiver>
220
221        <service
221-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
222            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
222-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
223            android:exported="false" >
223-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
224            <meta-data
224-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
225                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
225-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
226                android:value="cct" />
226-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\1765bffbe09c6c89377147d6eebd2da9\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
227        </service>
228        <service
228-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
229            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
229-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
230            android:exported="false"
230-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
231            android:permission="android.permission.BIND_JOB_SERVICE" >
231-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
232        </service>
233
234        <receiver
234-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
235            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
235-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
236            android:exported="false" />
236-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\075e6ab24395c9421254120dd873eb9b\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
237    </application>
238
239</manifest>
