<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.google.mlkit:text-recognition:16.0.1" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets"><file name="mlkit-google-ocr-models/aksara/aksara_page_layout_analysis_rpn_gcn.binarypb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\aksara\aksara_page_layout_analysis_rpn_gcn.binarypb"/><file name="mlkit-google-ocr-models/aksara/aksara_page_layout_analysis_ti_rpn_gcn.binarypb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\aksara\aksara_page_layout_analysis_ti_rpn_gcn.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/assets.extra/LabelMap.pb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\assets.extra\LabelMap.pb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/conv_model.fb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\conv_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/lstm_model.fb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\lstm_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc_cpu.binarypb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc_cpu.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/tflite_langid.tflite" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\tflite_langid.tflite"/><file name="mlkit-google-ocr-models/gocr/layout/line_clustering_custom_ops/model.tflite" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\gocr\layout\line_clustering_custom_ops\model.tflite"/><file name="mlkit-google-ocr-models/gocr/layout/line_splitting_custom_ops/model.tflite" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\gocr\layout\line_splitting_custom_ops\model.tflite"/><file name="mlkit-google-ocr-models/taser/detector/region_proposal_text_detector_tflite_vertical_mbv2_v1.bincfg" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\taser\detector\region_proposal_text_detector_tflite_vertical_mbv2_v1.bincfg"/><file name="mlkit-google-ocr-models/taser/detector/rpn_text_detector_mobile_space_to_depth_quantized_mbv2_v1.tflite" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\taser\detector\rpn_text_detector_mobile_space_to_depth_quantized_mbv2_v1.tflite"/><file name="mlkit-google-ocr-models/taser/rpn_text_detection_tflite_mobile_mbv2.binarypb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\taser\rpn_text_detection_tflite_mobile_mbv2.binarypb"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.bincfg" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.bincfg"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.conv_model" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.conv_model"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.lstm_model" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.lstm_model"/><file name="mlkit-google-ocr-models/taser/taser_script_identification_tflite_mobile.binarypb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\taser\taser_script_identification_tflite_mobile.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_engine.binarypb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_engine.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_engine_ti.binarypb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_engine_ti.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_recognizer.binarypb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_recognizer.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_runner.binarypb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_runner.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_runner_ti.binarypb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\cba1d796a5bea0fc02e7dd5f6537fb89\transformed\jetified-text-recognition-16.0.1\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_runner_ti.binarypb"/></source></dataSet><dataSet config=":permission_handler_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\permission_handler_android\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\path_provider_android\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":google_mlkit_commons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\google_mlkit_commons\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":google_mlkit_text_recognition" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\google_mlkit_text_recognition\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\flutter_plugin_android_lifecycle\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":camera_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\camera_android\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":image_picker_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\image_picker_android\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":file_picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\file_picker\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":share_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\share_plus\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\remote_designer\build\app\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>