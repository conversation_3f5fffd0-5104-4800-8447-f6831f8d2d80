import 'package:flutter/material.dart';
import '../models/button.dart';
import '../services/ir_code_library.dart';

class IrCodeBrowserScreen extends StatefulWidget {
  final Function(IrCode) onCodeSelected;

  const IrCodeBrowserScreen({
    super.key,
    required this.onCodeSelected,
  });

  @override
  State<IrCodeBrowserScreen> createState() => _IrCodeBrowserScreenState();
}

class _IrCodeBrowserScreenState extends State<IrCodeBrowserScreen> {
  String? _selectedBrand;
  String? _selectedDeviceType;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('IR Code Library'),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by button name (e.g., power, volume)',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
        ),
      ),
      body: _searchQuery.isNotEmpty ? _buildSearchResults() : _buildBrandSelection(),
    );
  }

  Widget _buildSearchResults() {
    final searchResults = IrCodeLibrary.searchByButton(_searchQuery.toLowerCase());
    
    if (searchResults.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No codes found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            Text(
              'Try searching for: power, volume, channel, mute',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: searchResults.length,
      itemBuilder: (context, index) {
        final entry = searchResults.entries.elementAt(index);
        final deviceInfo = entry.key;
        final codes = entry.value;
        
        return Card(
          margin: const EdgeInsets.all(8),
          child: ExpansionTile(
            title: Text(deviceInfo),
            subtitle: Text('${codes.length} code(s) found'),
            children: codes.entries.map((codeEntry) {
              final buttonName = codeEntry.key;
              final irCode = codeEntry.value;
              
              return ListTile(
                title: Text(buttonName.toUpperCase()),
                subtitle: Text('${irCode.type} - ${irCode.freq}Hz - ${irCode.pattern.length} pulses'),
                trailing: ElevatedButton(
                  onPressed: () {
                    widget.onCodeSelected(irCode);
                    Navigator.of(context).pop();
                  },
                  child: const Text('Select'),
                ),
              );
            }).toList(),
          ),
        );
      },
    );
  }

  Widget _buildBrandSelection() {
    if (_selectedBrand == null) {
      return _buildBrandList();
    } else if (_selectedDeviceType == null) {
      return _buildDeviceTypeList();
    } else {
      return _buildCodeList();
    }
  }

  Widget _buildBrandList() {
    final brands = IrCodeLibrary.getBrands();
    
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          child: const Text(
            'Select Brand',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: brands.length,
            itemBuilder: (context, index) {
              final brand = brands[index];
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                child: ListTile(
                  title: Text(brand),
                  subtitle: Text('${IrCodeLibrary.getDeviceTypes(brand).length} device types'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    setState(() {
                      _selectedBrand = brand;
                    });
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDeviceTypeList() {
    final deviceTypes = IrCodeLibrary.getDeviceTypes(_selectedBrand!);
    
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  setState(() {
                    _selectedBrand = null;
                  });
                },
              ),
              Expanded(
                child: Text(
                  '$_selectedBrand - Select Device Type',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: deviceTypes.length,
            itemBuilder: (context, index) {
              final deviceType = deviceTypes[index];
              final codeCount = IrCodeLibrary.getAllCodes(_selectedBrand!, deviceType).length;
              
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                child: ListTile(
                  title: Text(deviceType),
                  subtitle: Text('$codeCount codes available'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    setState(() {
                      _selectedDeviceType = deviceType;
                    });
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCodeList() {
    final codes = IrCodeLibrary.getAllCodes(_selectedBrand!, _selectedDeviceType!);
    
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  setState(() {
                    _selectedDeviceType = null;
                  });
                },
              ),
              Expanded(
                child: Text(
                  '$_selectedBrand $_selectedDeviceType',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: codes.length,
            itemBuilder: (context, index) {
              final entry = codes.entries.elementAt(index);
              final buttonName = entry.key;
              final irCode = entry.value;
              
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                child: ListTile(
                  title: Text(buttonName.toUpperCase()),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Protocol: ${irCode.type}'),
                      Text('Frequency: ${irCode.freq}Hz'),
                      Text('Pattern Length: ${irCode.pattern.length} pulses'),
                    ],
                  ),
                  trailing: ElevatedButton(
                    onPressed: () {
                      widget.onCodeSelected(irCode);
                      Navigator.of(context).pop();
                    },
                    child: const Text('Select'),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
