<component name="libraryTable">
  <library name="Flutter Plugins" type="FlutterPluginsLibraryType">
    <CLASSES>
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_ios-0.8.13" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/share_plus-9.0.0" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/camera_android-0.10.10+7" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_windows-0.2.2" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_android-0.8.13+3" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/google_mlkit_commons-0.8.1" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_for_web-3.1.0" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker-1.2.0" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_linux-0.2.2" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/permission_handler_html-0.1.3+5" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/google_mlkit_text_recognition-0.13.1" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/permission_handler-11.4.0" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+4" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/camera-0.10.6" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path_provider_android-2.2.18" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path_provider-2.1.5" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.2" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/camera_web-0.3.5" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/camera_avfoundation-0.9.21+3" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.4" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/file_picker-8.0.7" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_macos-0.2.2" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+4" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/url_launcher_web-2.4.1" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>