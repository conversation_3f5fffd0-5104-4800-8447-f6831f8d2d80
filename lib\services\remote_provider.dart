import 'package:flutter/foundation.dart';
import '../models/remote_profile.dart';
import '../models/button.dart';
import 'storage_service.dart';

class RemoteProvider extends ChangeNotifier {
  final StorageService _storageService = StorageService();
  List<RemoteProfile> _remotes = [];
  RemoteProfile? _currentRemote;
  bool _isLoading = false;

  List<RemoteProfile> get remotes => _remotes;
  RemoteProfile? get currentRemote => _currentRemote;
  bool get isLoading => _isLoading;

  Future<void> init() async {
    try {
      await _storageService.init();
      await loadRemotes();
    } catch (e) {
      print('Error initializing RemoteProvider: $e');
      // Initialize with empty list if loading fails
      _remotes = [];
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadRemotes() async {
    _isLoading = true;
    notifyListeners();

    try {
      _remotes = await _storageService.loadRemotes();
    } catch (e) {
      print('Error loading remotes: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> saveRemote(RemoteProfile remote) async {
    try {
      await _storageService.saveRemote(remote);
      
      // Update local list
      final index = _remotes.indexWhere((r) => r.remoteId == remote.remoteId);
      if (index >= 0) {
        _remotes[index] = remote;
      } else {
        _remotes.add(remote);
      }
      
      notifyListeners();
    } catch (e) {
      print('Error saving remote: $e');
      rethrow;
    }
  }

  Future<void> deleteRemote(String remoteId) async {
    try {
      await _storageService.deleteRemote(remoteId);
      _remotes.removeWhere((r) => r.remoteId == remoteId);
      
      if (_currentRemote?.remoteId == remoteId) {
        _currentRemote = null;
      }
      
      notifyListeners();
    } catch (e) {
      print('Error deleting remote: $e');
      rethrow;
    }
  }

  void setCurrentRemote(RemoteProfile? remote) {
    _currentRemote = remote;
    notifyListeners();
  }

  void updateCurrentRemote(RemoteProfile remote) {
    _currentRemote = remote;
    notifyListeners();
  }

  void addButtonToCurrentRemote(Button button) {
    if (_currentRemote != null) {
      final updatedButtons = List<Button>.from(_currentRemote!.buttons)..add(button);
      _currentRemote = RemoteProfile(
        remoteId: _currentRemote!.remoteId,
        deviceName: _currentRemote!.deviceName,
        sourceImage: _currentRemote!.sourceImage,
        imageW: _currentRemote!.imageW,
        imageH: _currentRemote!.imageH,
        buttons: updatedButtons,
      );
      notifyListeners();
    }
  }

  void updateButtonInCurrentRemote(String buttonId, Button updatedButton) {
    if (_currentRemote != null) {
      final updatedButtons = _currentRemote!.buttons.map((button) {
        return button.id == buttonId ? updatedButton : button;
      }).toList();
      
      _currentRemote = RemoteProfile(
        remoteId: _currentRemote!.remoteId,
        deviceName: _currentRemote!.deviceName,
        sourceImage: _currentRemote!.sourceImage,
        imageW: _currentRemote!.imageW,
        imageH: _currentRemote!.imageH,
        buttons: updatedButtons,
      );
      notifyListeners();
    }
  }

  void removeButtonFromCurrentRemote(String buttonId) {
    if (_currentRemote != null) {
      final updatedButtons = _currentRemote!.buttons.where((button) => button.id != buttonId).toList();
      _currentRemote = RemoteProfile(
        remoteId: _currentRemote!.remoteId,
        deviceName: _currentRemote!.deviceName,
        sourceImage: _currentRemote!.sourceImage,
        imageW: _currentRemote!.imageW,
        imageH: _currentRemote!.imageH,
        buttons: updatedButtons,
      );
      notifyListeners();
    }
  }

  // Create predefined common buttons
  List<Button> createCommonButtons(double imageWidth, double imageHeight) {
    final commonButtons = [
      {'label': 'Power', 'icon': 'power', 'color': '#ff0000'},
      {'label': 'Vol+', 'icon': 'volume_up', 'color': '#4CAF50'},
      {'label': 'Vol-', 'icon': 'volume_down', 'color': '#4CAF50'},
      {'label': 'Ch+', 'icon': 'keyboard_arrow_up', 'color': '#2196F3'},
      {'label': 'Ch-', 'icon': 'keyboard_arrow_down', 'color': '#2196F3'},
      {'label': 'Mute', 'icon': 'volume_off', 'color': '#FF9800'},
      {'label': 'Menu', 'icon': 'menu', 'color': '#9C27B0'},
      {'label': 'OK', 'icon': 'check_circle', 'color': '#4CAF50'},
      {'label': 'Up', 'icon': 'keyboard_arrow_up', 'color': '#607D8B'},
      {'label': 'Down', 'icon': 'keyboard_arrow_down', 'color': '#607D8B'},
      {'label': 'Left', 'icon': 'keyboard_arrow_left', 'color': '#607D8B'},
      {'label': 'Right', 'icon': 'keyboard_arrow_right', 'color': '#607D8B'},
    ];

    return commonButtons.asMap().entries.map((entry) {
      final index = entry.key;
      final buttonData = entry.value;
      
      return Button(
        id: 'common_${index}_${DateTime.now().millisecondsSinceEpoch}',
        label: buttonData['label']!,
        icon: buttonData['icon'],
        bbox: BoundingBox(
          x: 50 + (index % 4) * 80.0,
          y: 50 + (index ~/ 4) * 60.0,
          w: 70,
          h: 50,
        ),
        style: Style(
          bgColor: buttonData['color']!,
          textColor: '#ffffff',
        ),
      );
    }).toList();
  }
}
