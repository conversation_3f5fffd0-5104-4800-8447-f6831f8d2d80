import 'package:flutter/material.dart';
import '../models/button.dart';

class DraggableButtonWidget extends StatefulWidget {
  final Button button;
  final bool isSelected;
  final VoidCallback onTap;
  final Function(Offset) onPositionChanged;

  const DraggableButtonWidget({
    super.key,
    required this.button,
    required this.isSelected,
    required this.onTap,
    required this.onPositionChanged,
  });

  @override
  State<DraggableButtonWidget> createState() => _DraggableButtonWidgetState();
}

class _DraggableButtonWidgetState extends State<DraggableButtonWidget> {
  late Offset _position;

  @override
  void initState() {
    super.initState();
    _position = Offset(widget.button.bbox.x, widget.button.bbox.y);
  }

  @override
  void didUpdateWidget(DraggableButtonWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.button.bbox.x != widget.button.bbox.x ||
        oldWidget.button.bbox.y != widget.button.bbox.y) {
      _position = Offset(widget.button.bbox.x, widget.button.bbox.y);
    }
  }

  Color _parseColor(String colorString) {
    try {
      return Color(int.parse(colorString.replaceFirst('#', '0xff')));
    } catch (e) {
      return Colors.blue;
    }
  }

  IconData? _getIconData(String? iconName) {
    if (iconName == null) return null;
    
    switch (iconName.toLowerCase()) {
      case 'power':
        return Icons.power_settings_new;
      case 'volume_up':
        return Icons.volume_up;
      case 'volume_down':
        return Icons.volume_down;
      case 'volume_off':
        return Icons.volume_off;
      case 'keyboard_arrow_up':
        return Icons.keyboard_arrow_up;
      case 'keyboard_arrow_down':
        return Icons.keyboard_arrow_down;
      case 'keyboard_arrow_left':
        return Icons.keyboard_arrow_left;
      case 'keyboard_arrow_right':
        return Icons.keyboard_arrow_right;
      case 'menu':
        return Icons.menu;
      case 'check_circle':
        return Icons.check_circle;
      case 'play_arrow':
        return Icons.play_arrow;
      case 'pause':
        return Icons.pause;
      case 'stop':
        return Icons.stop;
      case 'fast_forward':
        return Icons.fast_forward;
      case 'fast_rewind':
        return Icons.fast_rewind;
      case 'skip_next':
        return Icons.skip_next;
      case 'skip_previous':
        return Icons.skip_previous;
      case 'home':
        return Icons.home;
      case 'settings':
        return Icons.settings;
      default:
        return Icons.radio_button_unchecked;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _position.dx,
      top: _position.dy,
      child: GestureDetector(
        onTap: widget.onTap,
        onPanUpdate: (details) {
          setState(() {
            _position += details.delta;
          });
        },
        onPanEnd: (details) {
          widget.onPositionChanged(_position);
        },
        child: Container(
          width: widget.button.bbox.w,
          height: widget.button.bbox.h,
          decoration: BoxDecoration(
            color: _parseColor(widget.button.style.bgColor),
            borderRadius: BorderRadius.circular(8),
            border: widget.isSelected
                ? Border.all(color: Colors.orange, width: 3)
                : Border.all(color: Colors.white.withOpacity(0.5), width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 4,
                offset: const Offset(2, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              // Button content
              Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (widget.button.icon != null) ...[
                      Icon(
                        _getIconData(widget.button.icon),
                        color: _parseColor(widget.button.style.textColor),
                        size: widget.button.bbox.h > 40 ? 20 : 16,
                      ),
                      if (widget.button.bbox.h > 40)
                        const SizedBox(height: 2),
                    ],
                    if (widget.button.bbox.h > 30)
                      Text(
                        widget.button.label,
                        style: TextStyle(
                          color: _parseColor(widget.button.style.textColor),
                          fontSize: widget.button.bbox.h > 40 ? 12 : 10,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),
              
              // IR indicator
              if (widget.button.ir != null)
                Positioned(
                  top: 2,
                  right: 2,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              
              // Selection handles
              if (widget.isSelected) ...[
                // Top-left handle
                Positioned(
                  top: -4,
                  left: -4,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.orange,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
                // Top-right handle
                Positioned(
                  top: -4,
                  right: -4,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.orange,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
                // Bottom-left handle
                Positioned(
                  bottom: -4,
                  left: -4,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.orange,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
                // Bottom-right handle
                Positioned(
                  bottom: -4,
                  right: -4,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.orange,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
