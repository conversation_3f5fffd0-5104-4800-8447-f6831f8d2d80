import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:share_plus/share_plus.dart';
import '../services/remote_provider.dart';
import '../services/storage_service.dart';
import '../services/ir_service.dart';
import '../services/usb_ir_service.dart';
import '../models/button.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final StorageService _storageService = StorageService();
  final IrService _irService = IrService();
  bool _hasIrCapability = false;
  List<String> _availableIrMethods = [];
  String _testResults = '';
  bool _isTestingIr = false;
  bool _isTestingUsb = false;

  @override
  void initState() {
    super.initState();
    _checkIrCapabilities();
  }

  Future<void> _checkIrCapabilities() async {
    final hasIr = await _irService.hasIrTransmitter();
    final methods = await _irService.getAvailableIrMethods();

    setState(() {
      _hasIrCapability = hasIr;
      _availableIrMethods = methods;
    });
  }

  Future<void> _testBuiltInIr() async {
    setState(() {
      _isTestingIr = true;
      _testResults = 'Testing built-in IR transmitter...\n';
    });

    try {
      // Test IR capability
      final hasIr = await _irService.hasIrTransmitter();
      _appendTestResult('Built-in IR Support: ${hasIr ? "✅ Available" : "❌ Not Available"}');

      if (hasIr) {
        // Test IR transmission with a simple power code
        final testCode = IrCode(
          type: 'NEC',
          freq: 38000,
          pattern: [9000, 4500, 560, 560, 560, 1690, 560, 560, 560, 1690],
        );

        final success = await _irService.transmitIr(testCode);
        _appendTestResult('IR Transmission Test: ${success ? "✅ Success" : "❌ Failed"}');
      }

      // Get available methods
      final methods = await _irService.getAvailableIrMethods();
      _appendTestResult('Available IR Methods: ${methods.join(", ")}');

    } catch (e) {
      _appendTestResult('❌ Error testing IR: $e');
    } finally {
      setState(() {
        _isTestingIr = false;
      });
    }
  }

  Future<void> _testUsbIr() async {
    setState(() {
      _isTestingUsb = true;
      _testResults = 'Testing USB IR devices...\n';
    });

    try {
      // First, get ALL connected USB devices (not just IR ones)
      _appendTestResult('🔍 Scanning all USB devices...');

      // Check for USB IR dongles
      final hasUsbDongle = await _irService.checkUsbIrDongle();
      _appendTestResult('USB IR Dongle Detection: ${hasUsbDongle ? "✅ Found" : "❌ Not Found"}');

      // Get connected USB devices
      final devices = await _irService.getUsbIrDevices();
      _appendTestResult('Connected USB IR Devices: ${devices.length}');

      // Also try to get raw USB device list from Android
      _appendTestResult('\n📱 Checking Android USB Manager...');
      await _checkAndroidUsbDevices();

      if (hasUsbDongle && devices.isNotEmpty) {
        _appendTestResult('\n🔧 Testing USB IR devices:');
        for (int i = 0; i < devices.length; i++) {
          final device = devices[i];
          _appendTestResult('Device ${i + 1}: ${device.name}');
          _appendTestResult('  - Vendor ID: 0x${device.vendorId.toRadixString(16).toUpperCase()}');
          _appendTestResult('  - Product ID: 0x${device.productId.toRadixString(16).toUpperCase()}');
          _appendTestResult('  - Device Path: ${device.devicePath}');
          _appendTestResult('  - Can Transmit: ${device.canTransmit ? "✅" : "❌"}');
          _appendTestResult('  - Can Receive: ${device.canReceive ? "✅" : "❌"}');

          // Test connection to first device
          if (i == 0) {
            _appendTestResult('  - Testing connection...');
            final connected = await _irService.connectToUsbDevice(device);
            _appendTestResult('  - Connection Test: ${connected ? "✅ Success" : "❌ Failed"}');

            if (connected) {
              final testResult = await _irService.testUsbConnection();
              _appendTestResult('  - Communication Test: ${testResult ? "✅ Success" : "❌ Failed"}');
            }
          }
        }
      } else {
        _appendTestResult('\n💡 Tips for USB IR detection:');
        _appendTestResult('• Make sure USB IR dongle is properly connected');
        _appendTestResult('• Try different USB ports');
        _appendTestResult('• Check if device requires specific drivers');
        _appendTestResult('• Some devices may need OTG adapter');
      }

    } catch (e) {
      _appendTestResult('❌ Error testing USB IR: $e');
    } finally {
      setState(() {
        _isTestingUsb = false;
      });
    }
  }

  Future<void> _checkAndroidUsbDevices() async {
    try {
      // This will call Android's UsbManager to list all connected devices
      final result = await _irService.getUsbIrDevices();
      if (result.isEmpty) {
        _appendTestResult('Android USB Manager: No devices detected');
        _appendTestResult('This could mean:');
        _appendTestResult('• No USB devices connected');
        _appendTestResult('• USB debugging not enabled');
        _appendTestResult('• Device needs USB Host mode');
        _appendTestResult('• Missing USB permissions');
      } else {
        _appendTestResult('Android USB Manager: ${result.length} device(s) found');
      }
    } catch (e) {
      _appendTestResult('Android USB Manager Error: $e');
    }
  }

  void _appendTestResult(String result) {
    setState(() {
      _testResults += '$result\n';
    });
  }

  void _clearTestResults() {
    setState(() {
      _testResults = '';
    });
  }

  Future<void> _testAllUsbDevices() async {
    setState(() {
      _testResults = 'Scanning ALL USB devices...\n';
    });

    try {
      _appendTestResult('🔍 Comprehensive USB Device Scan');
      _appendTestResult('=====================================');

      // Try to get all USB devices through different methods
      _appendTestResult('\n📱 Method 1: USB IR Service');
      final irDevices = await _irService.getUsbIrDevices();
      _appendTestResult('USB IR devices found: ${irDevices.length}');

      for (int i = 0; i < irDevices.length; i++) {
        final device = irDevices[i];
        _appendTestResult('IR Device ${i + 1}:');
        _appendTestResult('  Name: ${device.name}');
        _appendTestResult('  Vendor: 0x${device.vendorId.toRadixString(16).toUpperCase()}');
        _appendTestResult('  Product: 0x${device.productId.toRadixString(16).toUpperCase()}');
        _appendTestResult('  Path: ${device.devicePath}');
      }

      _appendTestResult('\n🔧 Method 2: Android USB Manager');
      // This will call the native Android code to list ALL USB devices
      await _scanNativeUsbDevices();

      _appendTestResult('\n💡 USB Troubleshooting Tips:');
      _appendTestResult('• Enable USB Debugging in Developer Options');
      _appendTestResult('• Try different USB cables');
      _appendTestResult('• Check if device needs OTG support');
      _appendTestResult('• Some devices need specific drivers');
      _appendTestResult('• USB-C devices may need adapter');

    } catch (e) {
      _appendTestResult('❌ Error scanning USB devices: $e');
    }
  }

  Future<void> _scanNativeUsbDevices() async {
    try {
      // This should call the native Android method to get ALL USB devices
      // We'll need to add this to MainActivity.kt
      _appendTestResult('Calling native USB device scanner...');

      // For now, we'll use the existing method but add more details
      final devices = await _irService.getUsbIrDevices();
      if (devices.isEmpty) {
        _appendTestResult('Native scan: No USB devices detected');
        _appendTestResult('Possible reasons:');
        _appendTestResult('• No USB devices connected');
        _appendTestResult('• USB Host mode not supported');
        _appendTestResult('• Missing USB permissions');
        _appendTestResult('• Device in wrong USB mode');
      } else {
        _appendTestResult('Native scan: Found ${devices.length} device(s)');
      }
    } catch (e) {
      _appendTestResult('Native USB scan error: $e');
    }
  }

  Future<void> _testIrTransmission() async {
    setState(() {
      _testResults = 'Testing IR transmission...\n';
    });

    try {
      _appendTestResult('🚀 IR Transmission Test');
      _appendTestResult('========================');

      // Test built-in IR first
      final hasBuiltIn = await _irService.hasIrTransmitter();
      _appendTestResult('Built-in IR available: ${hasBuiltIn ? "✅" : "❌"}');

      if (hasBuiltIn) {
        _appendTestResult('\n📡 Testing built-in IR transmission...');

        // Test with different IR codes
        final testCodes = [
          IrCode(type: 'NEC', freq: 38000, pattern: [9000, 4500, 560, 560, 560, 1690]),
          IrCode(type: 'Samsung', freq: 38000, pattern: [4500, 4500, 560, 1690, 560, 560]),
          IrCode(type: 'Sony', freq: 40000, pattern: [2400, 600, 600, 600, 1200, 600]),
        ];

        for (int i = 0; i < testCodes.length; i++) {
          final code = testCodes[i];
          _appendTestResult('Test ${i + 1} (${code.type} @ ${code.freq}Hz):');

          final success = await _irService.transmitIr(code);
          _appendTestResult('  Result: ${success ? "✅ Transmitted" : "❌ Failed"}');

          // Small delay between transmissions
          await Future.delayed(const Duration(milliseconds: 500));
        }

        _appendTestResult('\n💡 If you have an IR receiver nearby,');
        _appendTestResult('you should see IR signals being transmitted!');
      }

      // Test USB IR if available
      final usbDevices = await _irService.getUsbIrDevices();
      if (usbDevices.isNotEmpty) {
        _appendTestResult('\n🔌 Testing USB IR transmission...');
        final device = usbDevices.first;
        final connected = await _irService.connectToUsbDevice(device);

        if (connected) {
          final testCode = IrCode(type: 'NEC', freq: 38000, pattern: [9000, 4500, 560, 1690]);
          final success = await _irService.transmitIr(testCode);
          _appendTestResult('USB IR transmission: ${success ? "✅ Success" : "❌ Failed"}');
        } else {
          _appendTestResult('USB IR connection failed');
        }
      }

    } catch (e) {
      _appendTestResult('❌ Error testing IR transmission: $e');
    }
  }

  Future<void> _exportAllRemotes() async {
    final provider = context.read<RemoteProvider>();
    if (provider.remotes.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No remotes to export')),
      );
      return;
    }

    try {
      final List<File> exportedFiles = [];
      for (final remote in provider.remotes) {
        final file = await _storageService.exportRemote(remote);
        exportedFiles.add(file);
      }

      if (exportedFiles.isNotEmpty) {
        // Share the first file as an example
        await Share.shareXFiles(
          exportedFiles.map((f) => XFile(f.path)).toList(),
          text: 'RemoteDesigner profiles',
        );
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Exported ${exportedFiles.length} remotes')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Export failed: $e')),
      );
    }
  }

  Future<void> _importRemote() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final remote = await _storageService.importRemote(file);
        
        await context.read<RemoteProvider>().saveRemote(remote);
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Imported "${remote.deviceName}" successfully')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Import failed: $e')),
      );
    }
  }

  Future<void> _clearAllData() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'This will delete all saved remotes and cannot be undone. Are you sure?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final provider = context.read<RemoteProvider>();
        final remoteIds = provider.remotes.map((r) => r.remoteId).toList();
        
        for (final id in remoteIds) {
          await provider.deleteRemote(id);
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('All data cleared successfully')),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to clear data: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // IR Capabilities Section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'IR Capabilities',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Icon(
                        _hasIrCapability ? Icons.check_circle : Icons.cancel,
                        color: _hasIrCapability ? Colors.green : Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _hasIrCapability 
                            ? 'Built-in IR transmitter detected'
                            : 'No built-in IR transmitter',
                      ),
                    ],
                  ),
                  if (_availableIrMethods.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Text('Available methods:'),
                    ..._availableIrMethods.map((method) => Padding(
                      padding: const EdgeInsets.only(left: 16, top: 4),
                      child: Text('• $method'),
                    )),
                  ],
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // IR Testing Section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'IR Testing & Diagnostics',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 12),

                  // Test Buttons Row 1
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _isTestingIr ? null : _testBuiltInIr,
                          icon: _isTestingIr
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                              : const Icon(Icons.settings_remote),
                          label: Text(_isTestingIr ? 'Testing...' : 'Test Built-in IR'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _isTestingUsb ? null : _testUsbIr,
                          icon: _isTestingUsb
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                              : const Icon(Icons.usb),
                          label: Text(_isTestingUsb ? 'Testing...' : 'Test USB IR'),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Test Buttons Row 2
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: (_isTestingIr || _isTestingUsb) ? null : _testAllUsbDevices,
                          icon: const Icon(Icons.developer_mode),
                          label: const Text('Scan All USB'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: (_isTestingIr || _isTestingUsb) ? null : _testIrTransmission,
                          icon: const Icon(Icons.send),
                          label: const Text('Test IR Send'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // Clear Results Button
                  if (_testResults.isNotEmpty)
                    TextButton.icon(
                      onPressed: _clearTestResults,
                      icon: const Icon(Icons.clear),
                      label: const Text('Clear Results'),
                    ),

                  // Test Results Display
                  if (_testResults.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.terminal, size: 16),
                              const SizedBox(width: 8),
                              Text(
                                'Test Results',
                                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Container(
                            constraints: const BoxConstraints(maxHeight: 200),
                            child: SingleChildScrollView(
                              child: Text(
                                _testResults,
                                style: const TextStyle(
                                  fontFamily: 'monospace',
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Data Management Section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Data Management',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 12),
                  
                  ListTile(
                    leading: const Icon(Icons.file_upload),
                    title: const Text('Import Remote'),
                    subtitle: const Text('Import remote profile from JSON file'),
                    onTap: _importRemote,
                  ),
                  
                  ListTile(
                    leading: const Icon(Icons.file_download),
                    title: const Text('Export All Remotes'),
                    subtitle: const Text('Export all remotes as JSON files'),
                    onTap: _exportAllRemotes,
                  ),
                  
                  const Divider(),
                  
                  ListTile(
                    leading: const Icon(Icons.delete_forever, color: Colors.red),
                    title: const Text('Clear All Data'),
                    subtitle: const Text('Delete all saved remotes'),
                    onTap: _clearAllData,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // App Information Section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'About RemoteDesigner',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 12),
                  
                  const ListTile(
                    leading: Icon(Icons.info),
                    title: Text('Version'),
                    subtitle: Text('1.0.0'),
                  ),
                  
                  ListTile(
                    leading: const Icon(Icons.description),
                    title: const Text('Privacy Policy'),
                    subtitle: const Text('All processing is done locally on your device'),
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: const Text('Privacy Policy'),
                          content: const SingleChildScrollView(
                            child: Text(
                              'RemoteDesigner processes all data locally on your device. '
                              'No data is sent to external servers. '
                              'The app only supports IR signals and does not work with '
                              'RF, encrypted, or security-related signals for privacy and safety reasons.',
                            ),
                          ),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: const Text('OK'),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                  
                  ListTile(
                    leading: const Icon(Icons.warning),
                    title: const Text('Disclaimer'),
                    subtitle: const Text('IR signals only - no RF/encrypted signals'),
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: const Text('Important Disclaimer'),
                          content: const SingleChildScrollView(
                            child: Text(
                              'This app is designed for legitimate use with IR-controlled devices only. '
                              'It does not support and will not work with:\n\n'
                              '• Car key fobs or garage door openers\n'
                              '• Security systems or encrypted signals\n'
                              '• Any RF (Radio Frequency) devices\n'
                              '• Proprietary or copyrighted remote protocols\n\n'
                              'Use responsibly and only with devices you own.',
                            ),
                          ),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: const Text('Understood'),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
