import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/remote_profile.dart';
import '../models/button.dart';
import '../services/remote_provider.dart';
import '../services/image_analysis_service.dart';
import '../services/device_detection_service.dart';
import '../widgets/draggable_button_widget.dart';
import '../widgets/button_editor_dialog.dart';
import 'ir_learning_screen.dart';

class EditorScreen extends StatefulWidget {
  final File imageFile;
  final String deviceName;

  const EditorScreen({
    super.key,
    required this.imageFile,
    required this.deviceName,
  });

  @override
  State<EditorScreen> createState() => _EditorScreenState();
}

class _EditorScreenState extends State<EditorScreen> {
  final ImageAnalysisService _analysisService = ImageAnalysisService();
  final DeviceDetectionService _deviceDetectionService = DeviceDetectionService();
  bool _isAnalyzing = false;
  bool _showButtons = true;
  String? _selectedButtonId;
  double _imageWidth = 0;
  double _imageHeight = 0;
  DeviceDetectionResult? _detectionResult;

  @override
  void initState() {
    super.initState();
    _initializeRemote();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _detectDevice();
    });
  }

  void _initializeRemote() async {
    // Create a new remote profile
    final remoteId = 'remote_${DateTime.now().millisecondsSinceEpoch}';
    final remote = RemoteProfile(
      remoteId: remoteId,
      deviceName: widget.deviceName,
      sourceImage: widget.imageFile.path,
      imageW: 1.0, // Will be updated after image loads
      imageH: 1.0,
      buttons: [],
    );

    // Delay setting the remote until after the first frame is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<RemoteProvider>().setCurrentRemote(remote);
    });
    
    // Auto-analyze the image after the first frame is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _analyzeImage();
    });
  }

  Future<void> _analyzeImage() async {
    setState(() {
      _isAnalyzing = true;
    });

    try {
      final candidates = await _analysisService.analyzeImage(widget.imageFile);
      final buttons = _analysisService.candidatesToButtons(candidates);
      
      final provider = context.read<RemoteProvider>();
      final currentRemote = provider.currentRemote;
      
      if (currentRemote != null) {
        final updatedRemote = RemoteProfile(
          remoteId: currentRemote.remoteId,
          deviceName: currentRemote.deviceName,
          sourceImage: currentRemote.sourceImage,
          imageW: _imageWidth > 0 ? _imageWidth : 1.0,
          imageH: _imageHeight > 0 ? _imageHeight : 1.0,
          buttons: buttons,
        );
        provider.updateCurrentRemote(updatedRemote);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Analysis failed: $e')),
      );
    } finally {
      setState(() {
        _isAnalyzing = false;
      });
    }
  }

  void _detectDevice() async {
    try {
      final result = await _deviceDetectionService.detectDevice(widget.imageFile);
      setState(() {
        _detectionResult = result;
      });

      if (result.detectedBrand != null || result.detectedDeviceType != null) {
        _showDeviceDetectionDialog(result);
      }
    } catch (e) {
      print('Error detecting device: $e');
    }
  }

  void _showDeviceDetectionDialog(DeviceDetectionResult result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Device Detected'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (result.detectedBrand != null)
              Text('Brand: ${result.detectedBrand}'),
            if (result.detectedDeviceType != null)
              Text('Type: ${result.detectedDeviceType}'),
            Text('Confidence: ${(result.confidence * 100).toStringAsFixed(1)}%'),
            const SizedBox(height: 16),
            Text('Found ${result.suggestedCodes.length} suggested IR codes'),
            if (result.detectedTexts.isNotEmpty) ...[
              const SizedBox(height: 8),
              const Text('Detected text:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...result.detectedTexts.take(5).map((text) => Text('• $text')),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Skip'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _applySuggestedCodes(result);
            },
            child: const Text('Apply Codes'),
          ),
        ],
      ),
    );
  }

  void _applySuggestedCodes(DeviceDetectionResult result) {
    final provider = context.read<RemoteProvider>();

    // Create buttons with suggested IR codes
    for (final entry in result.suggestedCodes.entries) {
      final buttonName = entry.key;
      final irCode = entry.value;

      final button = Button(
        id: DateTime.now().millisecondsSinceEpoch.toString() + buttonName.hashCode.toString(),
        label: buttonName.replaceAll('_', ' ').toUpperCase(),
        bbox: BoundingBox(
          x: 0.1 + (result.suggestedCodes.keys.toList().indexOf(buttonName) % 3) * 0.3,
          y: 0.1 + (result.suggestedCodes.keys.toList().indexOf(buttonName) ~/ 3) * 0.15,
          w: 0.25,
          h: 0.1,
        ),
        style: Style(
          bgColor: Colors.blue.value.toRadixString(16).padLeft(8, '0'),
          textColor: Colors.white.value.toRadixString(16).padLeft(8, '0'),
          borderRadius: 8.0,
        ),
        ir: irCode,
      );

      provider.addButtonToCurrentRemote(button);
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Added ${result.suggestedCodes.length} buttons with IR codes'),
      ),
    );
  }

  void _addCommonButtons() {
    final provider = context.read<RemoteProvider>();
    final commonButtons = provider.createCommonButtons(_imageWidth, _imageHeight);

    for (final button in commonButtons) {
      provider.addButtonToCurrentRemote(button);
    }
  }

  void _addManualButton() {
    final provider = context.read<RemoteProvider>();
    final newButton = Button(
      id: 'manual_${DateTime.now().millisecondsSinceEpoch}',
      label: 'New Button',
      bbox: BoundingBox(
        x: 50,
        y: 50,
        w: 80,
        h: 50,
      ),
      style: Style(
        bgColor: '#2196F3',
        textColor: '#ffffff',
      ),
    );
    
    provider.addButtonToCurrentRemote(newButton);
  }

  void _editButton(Button button) {
    showDialog(
      context: context,
      builder: (context) => ButtonEditorDialog(
        button: button,
        onSave: (updatedButton) {
          context.read<RemoteProvider>().updateButtonInCurrentRemote(
            button.id,
            updatedButton,
          );
        },
      ),
    );
  }

  void _deleteButton(String buttonId) {
    context.read<RemoteProvider>().removeButtonFromCurrentRemote(buttonId);
    setState(() {
      _selectedButtonId = null;
    });
  }

  void _learnIrForButton(Button button) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => IrLearningScreen(button: button),
      ),
    );
  }

  Future<void> _saveRemote() async {
    final provider = context.read<RemoteProvider>();
    final remote = provider.currentRemote;
    
    if (remote != null) {
      try {
        await provider.saveRemote(remote);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Remote saved successfully!')),
        );
        Navigator.pop(context);
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to save remote: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Edit ${widget.deviceName}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.auto_fix_high),
            onPressed: _detectDevice,
            tooltip: 'Auto-detect device',
          ),
          IconButton(
            icon: Icon(_showButtons ? Icons.visibility : Icons.visibility_off),
            onPressed: () {
              setState(() {
                _showButtons = !_showButtons;
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveRemote,
          ),
        ],
      ),
      body: Consumer<RemoteProvider>(
        builder: (context, provider, child) {
          final remote = provider.currentRemote;
          if (remote == null) {
            return const Center(child: CircularProgressIndicator());
          }

          return Column(
            children: [
              // Analysis status
              if (_isAnalyzing)
                const LinearProgressIndicator(),
              
              // Image canvas with buttons
              Expanded(
                child: Stack(
                  children: [
                    // Background image
                    Center(
                      child: Image.file(
                        widget.imageFile,
                        fit: BoxFit.contain,
                        frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
                          if (frame != null) {
                            // Get image dimensions for proper scaling
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
                              if (renderBox != null) {
                                final size = renderBox.size;
                                setState(() {
                                  _imageWidth = size.width;
                                  _imageHeight = size.height;
                                });
                              }
                            });
                          }
                          return child;
                        },
                      ),
                    ),
                    
                    // Button overlays
                    if (_showButtons)
                      ...remote.buttons.map((button) {
                        return DraggableButtonWidget(
                          key: ValueKey(button.id),
                          button: button,
                          isSelected: _selectedButtonId == button.id,
                          onTap: () {
                            setState(() {
                              _selectedButtonId = button.id;
                            });
                          },
                          onPositionChanged: (newPosition) {
                            final updatedButton = Button(
                              id: button.id,
                              label: button.label,
                              icon: button.icon,
                              bbox: BoundingBox(
                                x: newPosition.dx,
                                y: newPosition.dy,
                                w: button.bbox.w,
                                h: button.bbox.h,
                              ),
                              style: button.style,
                              ir: button.ir,
                            );
                            provider.updateButtonInCurrentRemote(button.id, updatedButton);
                          },
                        );
                      }).toList(),
                  ],
                ),
              ),
              
              // Bottom toolbar
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  border: Border(top: BorderSide(color: Colors.grey[300]!)),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.auto_fix_high),
                      onPressed: _isAnalyzing ? null : _analyzeImage,
                      tooltip: 'Auto-detect buttons',
                    ),
                    IconButton(
                      icon: const Icon(Icons.add_box),
                      onPressed: _addManualButton,
                      tooltip: 'Add button',
                    ),
                    IconButton(
                      icon: const Icon(Icons.widgets),
                      onPressed: _addCommonButtons,
                      tooltip: 'Add common buttons',
                    ),
                    if (_selectedButtonId != null) ...[
                      IconButton(
                        icon: const Icon(Icons.edit),
                        onPressed: () {
                          final button = remote.buttons.firstWhere(
                            (b) => b.id == _selectedButtonId,
                          );
                          _editButton(button);
                        },
                        tooltip: 'Edit button',
                      ),
                      IconButton(
                        icon: const Icon(Icons.settings_remote),
                        onPressed: () {
                          final button = remote.buttons.firstWhere(
                            (b) => b.id == _selectedButtonId,
                          );
                          _learnIrForButton(button);
                        },
                        tooltip: 'Learn IR',
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () => _deleteButton(_selectedButtonId!),
                        tooltip: 'Delete button',
                      ),
                    ],
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _analysisService.dispose();
    _deviceDetectionService.dispose();
    super.dispose();
  }
}
