import '../models/button.dart';

class IrCodeLibrary {
  // Comprehensive IR code library organized by brand and device type
  static final Map<String, Map<String, Map<String, IrCode>>> _codeLibrary = {
    'Samsung': {
      'TV': {
        'power': IrCode(
          type: 'NEC',
          freq: 38000,
          pattern: [4500, 4500, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560],
        ),
        'volume_up': IrCode(
          type: 'NEC',
          freq: 38000,
          pattern: [4500, 4500, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560],
        ),
        'volume_down': IrCode(
          type: 'NEC',
          freq: 38000,
          pattern: [4500, 4500, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 560, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560],
        ),
        'channel_up': IrCode(
          type: 'NEC',
          freq: 38000,
          pattern: [4500, 4500, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 560, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560],
        ),
        'channel_down': IrCode(
          type: 'NEC',
          freq: 38000,
          pattern: [4500, 4500, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560],
        ),
        'mute': IrCode(
          type: 'NEC',
          freq: 38000,
          pattern: [4500, 4500, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 1690, 560, 560, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560],
        ),
      },
      'AC': {
        'power': IrCode(
          type: 'Samsung',
          freq: 38000,
          pattern: [3000, 9000, 4500, 560, 560, 1690, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560],
        ),
        'temp_up': IrCode(
          type: 'Samsung',
          freq: 38000,
          pattern: [3000, 9000, 4500, 560, 560, 1690, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 1690, 560, 560, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560],
        ),
        'temp_down': IrCode(
          type: 'Samsung',
          freq: 38000,
          pattern: [3000, 9000, 4500, 560, 560, 1690, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 1690, 560, 560, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560],
        ),
      },
    },
    'LG': {
      'TV': {
        'power': IrCode(
          type: 'NEC',
          freq: 38000,
          pattern: [9000, 4500, 560, 560, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 560, 560, 1690, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 1690, 560, 1690, 560, 560, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560],
        ),
        'volume_up': IrCode(
          type: 'NEC',
          freq: 38000,
          pattern: [9000, 4500, 560, 560, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 560, 560, 1690, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 1690, 560, 560, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560],
        ),
        'volume_down': IrCode(
          type: 'NEC',
          freq: 38000,
          pattern: [9000, 4500, 560, 560, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 560, 560, 1690, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560],
        ),
      },
    },
    'Sony': {
      'TV': {
        'power': IrCode(
          type: 'Sony',
          freq: 40000,
          pattern: [2400, 600, 1200, 600, 600, 600, 600, 600, 600, 600, 1200, 600, 600, 600, 600, 600, 600, 600, 600, 600, 1200, 600, 600, 600, 600, 25200],
        ),
        'volume_up': IrCode(
          type: 'Sony',
          freq: 40000,
          pattern: [2400, 600, 1200, 600, 600, 600, 600, 600, 600, 600, 1200, 600, 600, 600, 1200, 600, 600, 600, 600, 600, 1200, 600, 600, 600, 600, 25200],
        ),
        'volume_down': IrCode(
          type: 'Sony',
          freq: 40000,
          pattern: [2400, 600, 1200, 600, 600, 600, 600, 600, 600, 600, 1200, 600, 1200, 600, 1200, 600, 600, 600, 600, 600, 1200, 600, 600, 600, 600, 25200],
        ),
      },
    },
    'Panasonic': {
      'TV': {
        'power': IrCode(
          type: 'Panasonic',
          freq: 37000,
          pattern: [3500, 1750, 500, 400, 500, 1300, 500, 400, 500, 400, 500, 1300, 500, 400, 500, 400, 500, 400, 500, 1300, 500, 1300, 500, 400, 500, 1300, 500, 1300, 500, 1300, 500, 1300, 500, 400, 500, 400, 500, 400, 500, 1300, 500, 400, 500, 400, 500, 400, 500, 400, 500, 1300, 500, 1300, 500, 1300, 500, 400, 500, 1300, 500, 1300, 500, 1300, 500, 1300, 500],
        ),
      },
    },
    'Toshiba': {
      'TV': {
        'power': IrCode(
          type: 'NEC',
          freq: 38000,
          pattern: [9000, 4500, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 560, 560, 1690, 560, 560, 560, 560, 560, 1690, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560, 1690, 560],
        ),
      },
    },
  };

  // Get all available brands
  static List<String> getBrands() {
    return _codeLibrary.keys.toList()..sort();
  }

  // Get device types for a brand
  static List<String> getDeviceTypes(String brand) {
    return _codeLibrary[brand]?.keys.toList() ?? [];
  }

  // Get available buttons for a brand and device type
  static List<String> getAvailableButtons(String brand, String deviceType) {
    return _codeLibrary[brand]?[deviceType]?.keys.toList() ?? [];
  }

  // Get IR code for specific brand, device type, and button
  static IrCode? getIrCode(String brand, String deviceType, String button) {
    return _codeLibrary[brand]?[deviceType]?[button];
  }

  // Get all codes for a brand and device type
  static Map<String, IrCode> getAllCodes(String brand, String deviceType) {
    return _codeLibrary[brand]?[deviceType] ?? {};
  }

  // Search for codes by button name across all brands
  static Map<String, Map<String, IrCode>> searchByButton(String buttonName) {
    final results = <String, Map<String, IrCode>>{};
    
    for (final brand in _codeLibrary.keys) {
      for (final deviceType in _codeLibrary[brand]!.keys) {
        final codes = _codeLibrary[brand]![deviceType]!;
        if (codes.containsKey(buttonName)) {
          final key = '$brand $deviceType';
          results[key] = {buttonName: codes[buttonName]!};
        }
      }
    }
    
    return results;
  }

  // Get popular/common codes
  static Map<String, IrCode> getCommonCodes() {
    final commonCodes = <String, IrCode>{};
    
    // Get power codes from major brands
    for (final brand in ['Samsung', 'LG', 'Sony']) {
      final powerCode = getIrCode(brand, 'TV', 'power');
      if (powerCode != null) {
        commonCodes['${brand}_power'] = powerCode;
      }
    }
    
    return commonCodes;
  }

  // Auto-detect device brand from image analysis (placeholder)
  static String? detectBrandFromImage(String imagePath) {
    // This would use image recognition to detect brand logos
    // For now, return null (not implemented)
    return null;
  }

  // Get recommended codes based on device name
  static Map<String, IrCode> getRecommendedCodes(String deviceName) {
    final deviceLower = deviceName.toLowerCase();
    
    // Simple brand detection based on device name
    for (final brand in getBrands()) {
      if (deviceLower.contains(brand.toLowerCase())) {
        if (deviceLower.contains('tv') || deviceLower.contains('television')) {
          return getAllCodes(brand, 'TV');
        } else if (deviceLower.contains('ac') || deviceLower.contains('air')) {
          return getAllCodes(brand, 'AC');
        }
      }
    }
    
    // Return generic codes if no brand detected
    return getAllCodes('Samsung', 'TV'); // Default to Samsung TV
  }

  // Export codes to JSON format
  static Map<String, dynamic> exportCodes(String brand, String deviceType) {
    final codes = getAllCodes(brand, deviceType);
    final exportData = <String, dynamic>{};
    
    for (final entry in codes.entries) {
      exportData[entry.key] = entry.value.toJson();
    }
    
    return {
      'brand': brand,
      'deviceType': deviceType,
      'codes': exportData,
      'exportDate': DateTime.now().toIso8601String(),
    };
  }

  // Import codes from JSON format
  static bool importCodes(Map<String, dynamic> data) {
    try {
      final brand = data['brand'] as String;
      final deviceType = data['deviceType'] as String;
      final codesData = data['codes'] as Map<String, dynamic>;
      
      // This would require modifying the const library
      // For now, just validate the format
      for (final entry in codesData.entries) {
        IrCode.fromJson(entry.value as Map<String, dynamic>);
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }
}
