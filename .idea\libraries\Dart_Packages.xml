<component name="libraryTable">
  <library name="Dart Packages" type="DartPackagesLibraryType">
    <properties>
      <option name="packageNameToDirsMap">
        <entry key="async">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/async-2.13.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="boolean_selector">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/boolean_selector-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="camera">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/camera-0.10.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="camera_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/camera_android-0.10.10+7/lib" />
            </list>
          </value>
        </entry>
        <entry key="camera_avfoundation">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/camera_avfoundation-0.9.21+3/lib" />
            </list>
          </value>
        </entry>
        <entry key="camera_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/camera_platform_interface-2.11.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="camera_web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/camera_web-0.3.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="characters">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/characters-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="clock">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/clock-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="collection">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/collection-1.19.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cross_file">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/cross_file-0.3.4+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="crypto">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/crypto-3.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="fake_async">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/fake_async-1.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="ffi">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/ffi-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="file">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/file-7.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_picker">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/file_picker-8.0.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_linux">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_macos">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+4/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_windows">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib" />
            </list>
          </value>
        </entry>
        <entry key="fixnum">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/fixnum-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../flutter/packages/flutter/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_lints">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/flutter_lints-3.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_plugin_android_lifecycle">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.30/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_test">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../flutter/packages/flutter_test/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_web_plugins">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../flutter/packages/flutter_web_plugins/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_mlkit_commons">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/google_mlkit_commons-0.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_mlkit_text_recognition">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/google_mlkit_text_recognition-0.10.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="hive">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/hive-2.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="hive_flutter">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/hive_flutter-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="http">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/http-1.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_parser">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/http_parser-4.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker-1.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_android-0.8.13+3/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_for_web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_for_web-3.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_ios">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_ios-0.8.13/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_linux">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_linux-0.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_macos">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_macos-0.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.11.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_windows">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_windows-0.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/leak_tracker-11.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_flutter_testing">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.10/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_testing">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="lints">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/lints-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="matcher">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/matcher-0.12.17/lib" />
            </list>
          </value>
        </entry>
        <entry key="material_color_utilities">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="meta">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/meta-1.16.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="mime">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/mime-1.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="nested">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/nested-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path-1.9.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path_provider-2.1.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path_provider_android-2.2.18/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_foundation">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_linux">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_windows">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/permission_handler-11.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/permission_handler_android-12.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_apple">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/permission_handler_apple-9.4.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_html">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_windows">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="platform">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/platform-3.1.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="plugin_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="provider">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/provider-6.1.5+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="share_plus">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/share_plus-9.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="share_plus_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/share_plus_platform_interface-4.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sky_engine">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../flutter/bin/cache/pkg/sky_engine/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_span">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/source_span-1.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sprintf">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/sprintf-7.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="stack_trace">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/stack_trace-1.12.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_channel">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/stream_channel-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_transform">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/stream_transform-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="string_scanner">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/string_scanner-1.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="term_glyph">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/term_glyph-1.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="test_api">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/test_api-0.7.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="typed_data">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/typed_data-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_linux">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/url_launcher_web-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_windows">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="uuid">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/uuid-4.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_math">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/vector_math-2.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="vm_service">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/vm_service-15.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/web-0.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="win32">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/win32-5.14.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="xdg_directories">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0/lib" />
            </list>
          </value>
        </entry>
      </option>
    </properties>
    <CLASSES>
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/async-2.13.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/boolean_selector-2.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/camera-0.10.6/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/camera_android-0.10.10+7/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/camera_avfoundation-0.9.21+3/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/camera_platform_interface-2.11.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/camera_web-0.3.5/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/characters-1.4.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/clock-1.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/collection-1.19.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/cross_file-0.3.4+2/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/crypto-3.0.6/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/fake_async-1.3.3/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/ffi-2.1.4/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/file-7.0.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/file_picker-8.0.7/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+4/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/fixnum-1.1.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/flutter_lints-3.0.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.30/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/google_mlkit_commons-0.5.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/google_mlkit_text_recognition-0.10.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/hive-2.2.3/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/hive_flutter-1.1.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/http-1.5.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/http_parser-4.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker-1.2.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_android-0.8.13+3/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_for_web-3.1.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_ios-0.8.13/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_linux-0.2.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_macos-0.2.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.11.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/image_picker_windows-0.2.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/leak_tracker-11.0.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.10/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/lints-3.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/matcher-0.12.17/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/meta-1.16.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/mime-1.0.6/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/nested-1.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path-1.9.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path_provider-2.1.5/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path_provider_android-2.2.18/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/permission_handler-11.4.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/permission_handler_android-12.1.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/permission_handler_apple-9.4.7/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/platform-3.1.6/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/provider-6.1.5+1/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/share_plus-9.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/share_plus_platform_interface-4.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/source_span-1.10.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/sprintf-7.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/stack_trace-1.12.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/stream_channel-2.1.4/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/stream_transform-2.1.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/string_scanner-1.4.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/term_glyph-1.2.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/test_api-0.7.6/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/typed_data-1.4.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/url_launcher_web-2.4.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/uuid-4.5.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/vector_math-2.2.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/vm_service-15.0.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/web-0.5.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/win32-5.14.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../flutter/bin/cache/pkg/sky_engine/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../flutter/packages/flutter/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../flutter/packages/flutter_test/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../flutter/packages/flutter_web_plugins/lib" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>