import 'package:flutter/services.dart';
import '../models/button.dart';
import 'usb_ir_service.dart';
import 'ir_code_library.dart';

class IrService {
  static const MethodChannel _channel = MethodChannel('remotedesigner/ir');
  final UsbIrService _usbIrService = UsbIrService();

  UsbIrDevice? _connectedUsbDevice;

  // Check if device has IR transmitter capability
  Future<bool> hasIrTransmitter() async {
    try {
      final result = await _channel.invokeMethod('hasIrTransmitter');
      return result as bool;
    } catch (e) {
      print('Error checking IR capability: $e');
      return false;
    }
  }

  // Transmit IR signal (supports both built-in and USB devices)
  Future<bool> transmitIr(IrCode irCode) async {
    // Try USB device first if connected
    if (_connectedUsbDevice != null) {
      try {
        final result = await _usbIrService.transmitIrViaUsb(irCode);
        if (result) {
          return true;
        }
      } catch (e) {
        print('Error transmitting IR via USB: $e');
      }
    }

    // Fallback to built-in IR
    try {
      final result = await _channel.invokeMethod('transmitIr', {
        'frequency': irCode.freq,
        'pattern': irCode.pattern,
      });
      return result as bool;
    } catch (e) {
      print('Error transmitting IR: $e');
      return false;
    }
  }

  // Learn IR signal (supports both built-in and USB devices)
  Future<IrCode?> learnIrSignal() async {
    // Try USB device first if connected
    if (_connectedUsbDevice != null) {
      try {
        final result = await _usbIrService.learnIrViaUsb();
        if (result != null) {
          return result;
        }
      } catch (e) {
        print('Error learning IR via USB: $e');
      }
    }

    // Fallback to built-in IR (not supported by standard Android API)
    try {
      final result = await _channel.invokeMethod('learnIrSignal');
      if (result != null) {
        return IrCode(
          type: result['type'] as String,
          freq: result['frequency'] as int,
          pattern: List<int>.from(result['pattern'] as List),
        );
      }
      return null;
    } catch (e) {
      print('Error learning IR signal: $e');
      return null;
    }
  }

  // Get predefined IR codes for common buttons
  Map<String, IrCode> getPredefinedIrCodes() {
    return IrCodeLibrary.getCommonCodes();
  }

  // Get IR codes by brand and device type
  Map<String, IrCode> getIrCodesByBrand(String brand, String deviceType) {
    return IrCodeLibrary.getAllCodes(brand, deviceType);
  }

  // Get recommended codes based on device name
  Map<String, IrCode> getRecommendedCodes(String deviceName) {
    return IrCodeLibrary.getRecommendedCodes(deviceName);
  }

  // Get all available brands
  List<String> getAvailableBrands() {
    return IrCodeLibrary.getBrands();
  }

  // Get device types for a brand
  List<String> getDeviceTypes(String brand) {
    return IrCodeLibrary.getDeviceTypes(brand);
  }

  // Search codes by button name
  Map<String, Map<String, IrCode>> searchCodesByButton(String buttonName) {
    return IrCodeLibrary.searchByButton(buttonName);
  }

  // Check USB IR dongle connectivity
  Future<bool> checkUsbIrDongle() async {
    try {
      final devices = await _usbIrService.getConnectedDevices();
      print('USB IR Dongle Check:');
      print('Found ${devices.length} USB IR devices');
      devices.forEach((device) {
        print('Device: ${device.name}, Vendor: 0x${device.vendorId.toRadixString(16)}, Product: 0x${device.productId.toRadixString(16)}');
      });
      return devices.isNotEmpty;
    } catch (e) {
      print('Error checking USB IR dongle: $e');
      return false;
    }
  }

  // Get available USB IR devices
  Future<List<UsbIrDevice>> getUsbIrDevices() async {
    try {
      return await _usbIrService.getConnectedDevices();
    } catch (e) {
      print('Error getting USB IR devices: $e');
      return [];
    }
  }

  // Connect to USB IR device
  Future<bool> connectToUsbDevice(UsbIrDevice device) async {
    try {
      // Request permission first
      final hasPermission = await _usbIrService.requestPermission(device);
      if (!hasPermission) {
        return false;
      }

      // Connect to device
      final connected = await _usbIrService.connectToDevice(device);
      if (connected) {
        _connectedUsbDevice = device;
        return true;
      }
      return false;
    } catch (e) {
      print('Error connecting to USB device: $e');
      return false;
    }
  }

  // Disconnect from USB IR device
  Future<bool> disconnectFromUsbDevice() async {
    try {
      final result = await _usbIrService.disconnectFromDevice();
      if (result) {
        _connectedUsbDevice = null;
      }
      return result;
    } catch (e) {
      print('Error disconnecting from USB device: $e');
      return false;
    }
  }

  // Get connected USB device info
  UsbIrDevice? getConnectedUsbDevice() {
    return _connectedUsbDevice;
  }

  // Test USB device connection
  Future<bool> testUsbConnection() async {
    if (_connectedUsbDevice == null) {
      return false;
    }

    try {
      return await _usbIrService.testDeviceConnection();
    } catch (e) {
      print('Error testing USB connection: $e');
      return false;
    }
  }

  // Get available IR transmission methods
  Future<List<String>> getAvailableIrMethods() async {
    final List<String> methods = [];
    
    if (await hasIrTransmitter()) {
      methods.add('Built-in IR');
    }
    
    if (await checkUsbIrDongle()) {
      methods.add('USB IR Dongle');
    }
    
    return methods;
  }

  // Check Android version features
  Future<Map<String, bool>> checkAndroidVersionFeatures() async {
    try {
      final result = await _channel.invokeMethod('checkAndroidVersionFeatures');
      return Map<String, bool>.from(result);
    } catch (e) {
      print('Error checking Android version features: $e');
      return {
        'hasIrManager': false,
        'hasUsbManager': false,
        'hasSyncUsb': false,
        'hasAsyncUsb': false,
      };
    }
  }

  // Scan all connected devices for IR capability
  Future<List<Map<String, dynamic>>> scanAllConnectedDevices() async {
    try {
      final result = await _channel.invokeMethod('scanAllConnectedDevices');
      return List<Map<String, dynamic>>.from(result);
    } catch (e) {
      print('Error scanning connected devices: $e');
      return [];
    }
  }
}
