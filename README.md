# RemoteDesigner - Flutter App

## Overview

RemoteDesigner is a mobile application built with Flutter for Android (with future iOS support) that allows users to transform a photo of a real IR remote control into a fully functional, programmable virtual remote interface. The app analyzes the image to detect buttons, enables editing of the interface, records IR codes for each button, and transmits IR signals using the device's built-in IR blaster or a USB/Type-C IR dongle.

This project follows the MVP (Minimum Viable Product) specifications outlined in the project prompt, focusing on core features like image capture/analysis, button editing, IR learning/transmission, and JSON profile management.

### Target Users
- Android smartphone users wanting to digitize physical remotes.
- Hobbyists performing legitimate device maintenance and hacking for home IR appliances.
- Retailers/service providers creating custom remote profiles.

### Key Features (MVP)
1. Capture or upload remote image from camera/gallery.
2. Automatic image analysis to detect button candidates (bounding boxes, labels via OCR).
3. Interactive canvas editor for positioning, resizing, labeling, and styling buttons over the image.
4. IR code recording (learning mode) and transmission via ConsumerIrManager or USB IR dongle.
5. Local storage of remote profiles in JSON format.
6. List of saved remotes with import/export capabilities.
7. Predefined common buttons (e.g., power, volume, channel controls).
8. User-friendly UX for preview, testing, renaming, and customization.

### ✨ Advanced Features

#### 🔌 USB IR Dongle Support
- **Full USB Host API Integration**: Connect external USB IR transmitters and receivers
- **Device Auto-Detection**: Automatically detect compatible USB IR dongles
- **Permission Management**: Handle USB device permissions seamlessly
- **Learning Capability**: Learn IR signals from original remotes via USB devices
- **Transmission Support**: Send IR signals through USB dongles with custom protocols

#### 📚 Comprehensive IR Code Library
- **Multi-Brand Support**: Pre-built codes for Samsung, LG, Sony, Panasonic, Toshiba, and more
- **Device Type Coverage**: TV, AC, Set-Top Box, DVD/Blu-ray, Audio systems
- **Protocol Support**: NEC, Samsung, Sony, Panasonic, and RAW formats
- **Smart Search**: Browse codes by brand, device type, or button function
- **Export/Import**: Share IR code libraries between devices

#### 🤖 AI-Powered Device Detection
- **Brand Recognition**: Automatically detect device brand from remote images
- **Device Type Identification**: Distinguish between TV, AC, STB, and other devices
- **Smart Code Suggestions**: Auto-apply appropriate IR codes based on detected device
- **Text Analysis**: Extract and analyze text from remote images using Google ML Kit
- **Confidence Scoring**: Provide reliability scores for detection results

#### 🎯 Smart Features
- **Auto Button Layout**: Generate button layouts based on detected device type
- **Intelligent Naming**: Suggest button names based on detected text and device type
- **Pattern Recognition**: Identify common button patterns for different device types
- **Quick Setup**: One-click setup for popular device configurations

### Tech Stack
- **Framework**: Flutter (Dart) for cross-platform UI.
- **Packages**:
  - `image_picker` & `camera`: For image capture/upload.
  - `google_ml_kit`: For text recognition (OCR) on buttons.
  - `provider` or `riverpod`: State management.
  - `hive` or `sqflite`: Local storage.
  - `path_provider`: File handling.
  - Custom platform channels for native Android integration.
- **Native Android (Kotlin)**:
  - `ConsumerIrManager`: IR transmission.
  - USB Host API: For Type-C IR dongles.
  - OpenCV (via FFI or platform channels): Image processing for button detection.
- **Data Format**: JSON for remote profiles (see example below).
- **ML/CV**: OpenCV for contour detection (MVP fallback); future TFLite for button/icon detection.
- **Supported Android**: API 28+ (Android 9+).
- **Privacy**: Local processing only; no RF/encrypted signal support; clear UI warnings.

### JSON Profile Format Example
```json
{
  "remote_id": "ox_tv_202509",
  "device_name": "OX TV",
  "source_image": "ox_front.jpg",
  "image_w": 1800,
  "image_h": 600,
  "buttons": [
    {
      "id": "btn_01",
      "label": "Power",
      "icon": "power",
      "bbox": {"x":1200,"y":20,"w":90,"h":60},
      "style": {"bg_color":"#ff0000","text_color":"#ffffff"},
      "ir": {"type":"NEC","freq":38000,"pattern":[9000,4500,560,560,560,1690,...]}
    },
    {
      "id": "btn_02",
      "label": "Vol+",
      "bbox": {"x":1300,"y":150,"w":60,"h":60},
      "ir": {"type":"RAW","freq":38000,"pattern":[...]}
    }
  ]
}
```
- Coordinates are relative (normalized 0-1) for screen adaptability.
- IR patterns support NEC, RC5, Samsung, and RAW formats.

## Project Structure
```
remote_designer/
├── android/          # Native Android code (Kotlin platform channels)
│   ├── app/src/main/kotlin/.../MainActivity.kt
│   └── ...           # IR transmission, USB Host, OpenCV integration
├── lib/              # Dart/Flutter source
│   ├── main.dart
│   ├── screens/      # UI screens: home, capture, editor, ir_learning, player
│   ├── models/       # Data models: RemoteProfile, Button
│   ├── services/     # Image analysis, IR manager, storage
│   └── widgets/      # Reusable components: draggable buttons, canvas
├── assets/           # Images, icons
├── test/             # Unit/integration tests
├── pubspec.yaml      # Dependencies
└── README.md         # This file
```

## Setup Instructions

### Prerequisites
- Flutter SDK (v3.0+): Download from [flutter.dev](https://flutter.dev/docs/get-started/install).
- Android Studio: For emulator/device testing and native builds.
- Dart/Flutter extensions in VSCode.
- OpenCV for Android: Integrated via Gradle in `android/` (see build.gradle).
- Test devices: One with IR blaster (e.g., Xiaomi) and one for USB dongle testing.
- Python (optional): For prototyping CV with OpenCV (see `scripts/` if added).

### Installation
1. **Clone/Setup Project**:
   ```bash
   git clone <repo-url> remote_designer
   cd remote_designer
   ```

2. **Install Flutter Dependencies**:
   ```bash
   flutter pub get
   ```

3. **Android Setup**:
   - The project includes native Android code with IR support
   - Permissions are already configured in `AndroidManifest.xml`
   - Enable USB debugging on test devices
   - For physical IR testing: Ensure device supports `ConsumerIrManager`

4. **Run the App**:
   ```bash
   flutter run
   ```

## 📱 Usage Guide

### 📸 Basic Workflow
1. **Capture Remote**: Take a photo of your physical remote control
2. **Auto-Analysis**: The app automatically detects buttons and extracts text
3. **Device Detection**: AI automatically identifies device brand and type
4. **Smart Setup**: Choose to apply suggested IR codes or learn manually
5. **Edit Layout**: Drag, resize, and customize button positions
6. **Test & Save**: Test your virtual remote and save the profile
7. **Use Remote**: Access your saved remotes anytime to control your devices

### 🔌 USB IR Dongle Setup
1. **Connect Dongle**: Plug your USB IR dongle into the device
2. **Grant Permissions**: Allow USB device access when prompted
3. **Device Detection**: The app will automatically detect compatible dongles
4. **Learning Mode**: Use the dongle to learn IR signals from original remotes
5. **Transmission**: Send IR signals through the USB dongle

### 📚 Using the IR Code Library
1. **Browse Library**: Access the comprehensive IR code database
2. **Search by Brand**: Find codes for your specific device brand
3. **Filter by Type**: Select TV, AC, STB, or other device types
4. **Quick Apply**: Apply pre-built codes to your remote buttons
5. **Custom Search**: Search for specific button functions across all brands

### 🤖 Auto-Detection Features
1. **Smart Recognition**: The app automatically detects device information from images
2. **Brand Detection**: Identifies major brands like Samsung, LG, Sony, etc.
3. **Type Classification**: Distinguishes between TV, AC, and other device types
4. **Code Suggestions**: Automatically suggests appropriate IR codes
5. **One-Click Setup**: Apply detected configurations with a single tap
   - Select Android device/emulator
   - For IR testing: Use a device with built-in IR blaster (e.g., some Xiaomi phones)

5. **Build APK**:
   ```bash
   flutter build apk --release
   ```
   - Output: `build/app/outputs/flutter-apk/app-release.apk`

### Quick Start Guide
1. **Create Your First Remote**:
   - Tap the "+" button on the home screen
   - Take a photo of your remote or select from gallery
   - Enter a device name (e.g., "Samsung TV")
   - The app will automatically detect buttons using OCR

2. **Edit Your Remote**:
   - Drag buttons to reposition them
   - Tap a button to select it, then use the toolbar to:
     - Edit button properties (name, icon, color, size)
     - Learn IR codes from your original remote
     - Delete unwanted buttons
   - Add common buttons using the widgets icon
   - Save your remote when finished

3. **Use Your Remote**:
   - Tap on a saved remote from the home screen
   - Press buttons to transmit IR signals
   - Green dots indicate buttons with IR codes configured

4. **Import/Export**:
   - Go to Settings to import/export remote profiles
   - Share JSON files with other users
   - Backup your remotes for safekeeping

### Development Workflow
- **State Management**: Use Provider for simple global state (remotes list, current editor).
- **Image Analysis**:
  - Capture/Upload → Resize to fixed width → Grayscale → Blur → Canny edges → Contours.
  - Filter contours by area/aspect ratio → Crop & OCR each → Assign labels.
  - Platform channel to native for heavy CV (OpenCV).
- **Canvas Editor**: Use `CustomPainter` or `GestureDetector` with `Stack` for draggable/resizable overlays.
- **IR Handling**:
  - Transmit: `ConsumerIrManager.transmit(freq, pattern)`.
  - Learn: Manual input or USB dongle read (if supported); fallback to predefined codes.
- **Storage**: Hive for local JSON persistence; optional Firebase for cloud sync.

### User Flow
1. Home screen: List saved remotes + "Create New".
2. Capture/Upload image.
3. Auto-analysis progress → Edit screen (image background + button overlays).
4. Learn IR for buttons → Save as JSON.
5. Test transmission in player mode.
6. Export/share JSON.

### Testing
- **Unit Tests**: Button model, JSON serialization (`test/models/`).
- **Widget Tests**: Editor interactions (`test/widgets/`).
- **Integration**: Image analysis on 50+ remote photos (provide test assets in `assets/test_images/`).
- **Device Tests**: IR transmit on supported phones; USB dongle compatibility.
- **Performance**: Analysis <4s on mid-range devices.
- Run: `flutter test`.

### Known Limitations (MVP)
- No iOS support yet (IR dongles only).
- Basic CV (contours + OCR); no advanced ML.
- IR learning limited to supported hardware; no raw receive API.
- No RF/encrypted signals (privacy compliance).

### Future Enhancements
- TFLite model for button/icon detection.
- Remote profile marketplace.
- Theme editor and predefined layouts.
- iOS port with iCloud sync.

### Contributing
- Fork the repo, create a branch, submit PRs.
- Follow Dart/Flutter style guide.
- Add tests for new features.

### License
MIT License. See LICENSE file.

### Contact
For issues: Open a GitHub issue. For custom dongle support, provide hardware details.

---

For detailed specs, see the original project prompt. This README will be updated as the project evolves.
