# مشاكل الاتصال بـ ConsumerIrManager و IR Dongle - تحليل وحلول

## المشاكل الرئيسية

### 1. مشاكل في التعرف على ConsumerIrManager

#### المشكلة:
- في ملف `MainActivity.kt`، يتم التحقق من وجود `ConsumerIrManager` عبر `irManager.hasIrEmitter()`، لكن لا يوجد أي معالجة للأجهزة التي تدعم IR لكن لا تظهر في قائمة الأجهزة المدعومة.

#### الحل المقترح:
```kotlin
// تحسين دالة hasIrTransmitter في MainActivity.kt
private fun hasIrTransmitter(): Boolean {
    return try {
        val hasIrEmitter = irManager.hasIrEmitter()
        if (hasIrEmitter) {
            print("Device has built-in IR emitter")
            true
        } else {
            print("Device does not have built-in IR emitter")
            false
        }
    } catch (e: Exception) {
        print("Error checking IR emitter: ${e.message}")
        false
    }
}
```

### 2. مشاكل في التعرف على USB IR Dongle

#### المشكلة:
- دالة `isIrDevice` تعتمد فقط على قائمة ثابتة من Vendor/Product IDs، وهذا قد لا يغطي جميع أجهزة IR المتاحة.
- لا يتم التحقق من إصدار Android، وقد لا تعمل بعض الوظائف على إصدارات أقدم.
- في دالة `transmitIrViaUsb`، يتم استخدام `bulkTransfer` بشكل مباشر دون التحقق من نقاط النهاية (endpoints) بشكل صحيح.

#### الحل المقترح:
```kotlin
// تحسين دالة isIrDevice
private fun isIrDevice(device: UsbDevice): Boolean {
    // قائمة الأجهزة المعروفة
    val knownIrDevices = listOf(
        Pair(0x1234, 0x5678), // Generic USB IR
        Pair(0x2341, 0x0043), // Arduino Uno
        Pair(0x067b, 0x2303), // IrDA USB Adapter
        Pair(0x2341, 0x0010), // Arduino Mega
        Pair(0x1a86, 0x7523), // CH340 Serial Adapter
        Pair(0x10c4, 0xea60), // CP2102 USB to UART Bridge
        Pair(0x04d8, 0x00df), // Microchip (PIC) devices
        Pair(0x16c0, 0x0483), // VOTI shared PID
        Pair(0x16c0, 0x05df), // VOTI shared PID
        Pair(0x239a, 0x0001), // Adafruit Industries PID
    )

    // التحقق من الأجهزة المعروفة
    val isKnownIrDevice = knownIrDevices.any {
        it.first == device.vendorId && it.second == device.productId
    }

    // التحقق من اسم الجهاز
    val productName = device.productName?.lowercase() ?: ""
    val isNamedIrDevice = productName.contains("ir") || 
                          productName.contains("infrared") || 
                          productName.contains("remote") ||
                          productName.contains("transceiver")

    // التحقق من الفئات (Classes) والفئات الفرعية (Subclasses)
    val isIrByClass = (device.deviceClass == 0x02 && device.deviceSubclass == 0x02) || // Communications/Serial
                      (device.deviceClass == 0xff && device.deviceSubclass == 0x00)    // Vendor Specific

    val result = isKnownIrDevice || isNamedIrDevice || isIrByClass

    print("Device ${device.deviceName}:")
    print("  Vendor: 0x${device.vendorId.toString(16).padStart(4, '0')}, Product: 0x${device.productId.toString(16).padStart(4, '0')}")
    print("  Is Known: $isKnownIrDevice, Named: $isNamedIrDevice, Class: $isIrByClass")
    print("  Final Result: $result")

    return result
}

// تحسين دالة transmitIrViaUsb
private fun transmitIrViaUsb(frequency: Int, pattern: List<Int>, type: String): Boolean {
    return try {
        if (usbConnection == null || usbDevice == null) {
            print("USB connection not established")
            return false
        }

        // التحقق من وجود الواجهة ونقاط النهاية
        val interfaceCount = usbDevice!!.interfaceCount
        if (interfaceCount == 0) {
            print("Device has no interfaces")
            return false
        }

        val usbInterface = usbDevice!!.getInterface(0)
        val endpointCount = usbInterface.endpointCount
        if (endpointCount == 0) {
            print("Interface has no endpoints")
            return false
        }

        // البحث عن نقطة نهاية الإرسال (OUT endpoint)
        val outEndpoint = usbInterface.getEndpoint(0)
        if (!outEndpoint.direction == UsbConstants.USB_DIR_OUT) {
            print("Endpoint 0 is not an OUT endpoint")
            return false
        }

        // بناء أمر IR
        val command = buildIrCommand(frequency, pattern, type)

        // إرسال الأمر
        val result = usbConnection!!.bulkTransfer(outEndpoint, command, command.size, 1000)
        if (result >= 0) {
            print("Successfully transmitted IR command via USB")
            true
        } else {
            print("USB transmission failed with result: $result")
            false
        }
    } catch (e: Exception) {
        print("Error transmitting IR via USB: ${e.message}")
        false
    }
}
```

### 3. مشاكل في إدارة الأذونات

#### المشكلة:
- لا يتم طلب الأذونات بشكل صحيح لبعض الأجهزة.
- لا يتم التعامل مع حالة رفض الأذونات بشكل مناسب.

#### الحل المقترح:
```kotlin
// تحسين دالة requestUsbPermission
private fun requestUsbPermission(vendorId: Int, productId: Int, result: MethodChannel.Result) {
    val deviceList = usbManager.deviceList
    val device = deviceList.values.find {
        it.vendorId == vendorId && it.productId == productId
    }

    if (device == null) {
        print("USB device not found")
        result.success(false)
        return
    }

    if (usbManager.hasPermission(device)) {
        print("USB permission already granted")
        usbDevice = device
        usbConnection = usbManager.openDevice(device)
        result.success(usbConnection != null)
        return
    }

    try {
        val permissionIntent = PendingIntent.getBroadcast(
            this, 0, Intent(ACTION_USB_PERMISSION), PendingIntent.FLAG_IMMUTABLE
        )
        usbManager.requestPermission(device, permissionIntent)
        print("USB permission requested")
        result.success(true)
    } catch (e: Exception) {
        print("Error requesting USB permission: ${e.message}")
        result.error("USB_PERMISSION_ERROR", "Failed to request permission: ${e.message}", null)
    }
}
```

### 4. مشاكل في التعامل مع أنواع مختلفة من بروتوكولات IR

#### المشكلة:
- الكود الحالي لا يدعم بروتوكولات IR المختلفة بشكل كافٍ.
- لا يتم التعامل مع أنماط IR المعقدة.

#### الحل المقترح:
```kotlin
// إضافة دعم لبروتوكولات IR مختلفة
private fun buildIrCommand(frequency: Int, pattern: List<Int>, type: String): ByteArray {
    val command = mutableListOf<Byte>()

    // إضافة رأس الأمر
    command.add(0x02) // أمر إرسال IR

    // إضافة نوع البروتوكول
    val protocolType = when (type.uppercase()) {
        "NEC" -> 0x01
        "RC5" -> 0x02
        "RC6" -> 0x03
        "SONY" -> 0x04
        "SAMSUNG" -> 0x05
        "RAW" -> 0x00
        else -> 0x00 // RAW كإفتراضي
    }
    command.add(protocolType)

    // إضافة التردد (2 bytes, little endian)
    command.add((frequency and 0xFF).toByte())
    command.add(((frequency shr 8) and 0xFF).toByte())

    // إضافة طول النمط (2 bytes, little endian)
    command.add((pattern.size and 0xFF).toByte())
    command.add(((pattern.size shr 8) and 0xFF).toByte())

    // إضافة بيانات النمط
    for (value in pattern) {
        command.add((value and 0xFF).toByte())
        command.add(((value shr 8) and 0xFF).toByte())
    }

    return command.toByteArray()
}
```

## حلول إضافية

### 1. إضافة ميزة اكتشاف تلقائي للأجهزة

```kotlin
// إضافة دالة لفحص جميع الأجهزة المتصلة
private fun scanAllConnectedDevices(): List<Map<String, Any>> {
    val devices = mutableListOf<Map<String, Any>>()
    val deviceList = usbManager.deviceList

    print("Scanning all connected USB devices...")

    for (device in deviceList.values) {
        print("Found device: ${device.deviceName}")
        print("  Vendor: 0x${device.vendorId.toString(16)}, Product: 0x${device.productId.toString(16)}")

        // محاولة فتح الجهاز مباشرة للتحقق مما إذا كان جهاز IR
        if (usbManager.hasPermission(device)) {
            val connection = usbManager.openDevice(device)
            if (connection != null) {
                try {
                    // محاولة إرسال أمر بسيط للتحقق من الجهاز
                    val testCommand = byteArrayOf(0x00, 0x00)
                    val interfaceCount = device.interfaceCount
                    if (interfaceCount > 0) {
                        val endpointCount = device.getInterface(0).endpointCount
                        if (endpointCount > 0) {
                            val endpoint = device.getInterface(0).getEndpoint(0)
                            if (endpoint.direction == UsbConstants.USB_DIR_OUT) {
                                val result = connection.bulkTransfer(endpoint, testCommand, testCommand.size, 500)
                                if (result >= 0) {
                                    print("  Device responded to test - likely IR device")
                                    devices.add(mapOf(
                                        "name" to (device.productName ?: "Unknown Device"),
                                        "vendorId" to device.vendorId,
                                        "productId" to device.productId,
                                        "devicePath" to device.deviceName,
                                        "canTransmit" to true,
                                        "canReceive" to true,
                                        "isTested" to true
                                    ))
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    print("  Error testing device: ${e.message}")
                } finally {
                    connection.close()
                }
            }
        }
    }

    print("Found ${devices.size} potential IR devices")
    return devices
}
```

### 2. إضافة دعم أفضل لأنظمة التشغيل المختلفة

```kotlin
// التحقق من إصدار Android ودعم الميزات المناسبة
private fun checkAndroidVersionFeatures(): Map<String, Boolean> {
    val sdkVersion = android.os.Build.VERSION.SDK_INT
    val features = mutableMapOf<String, Boolean>()

    // دعم ConsumerIrManager
    features["hasIrManager"] = sdkVersion >= android.os.Build.VERSION_CODES.KITKAT

    // دعم UsbManager
    features["hasUsbManager"] = true // مدعوم من API 12

    // دعم UsbDeviceConnection للإرسال المتزامن
    features["hasSyncUsb"] = sdkVersion >= android.os.Build.VERSION_CODES.LOLLIPOP

    // دعم UsbRequest للإرسال غير المتزامن
    features["hasAsyncUsb"] = sdkVersion >= android.os.Build.VERSION_CODES.HONEYCOMB

    print("Android SDK Version: $sdkVersion")
    print("Available features: $features")

    return features
}
```

## خاتمة

المشاكل الرئيسية التي تمنع التطبيق من التعرف على اتصال بـ ConsumerIrManager أو عبر جهاز IR dongle تعود إلى:

1. قائمة محدودة من أجهزة IR المعروفة
2. مشاكل في التعامل مع نقاط النهاية (endpoints) بشكل صحيح
3. مشاكل في إدارة الأذونات
4. دعم محدود لبروتوكولات IR المختلفة

الحلول المقترحة تركز على تحسين هذه الجوانب وزيادة مرونة التطبيق في التعامل مع أنواع مختلفة من أجهزة IR. من المهم أيضاً إضافة المزيد من التسجيل (logging) لتسهيل استكشاف الأخطاء وإصلاحها.

