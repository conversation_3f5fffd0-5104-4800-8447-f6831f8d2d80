import 'package:flutter/material.dart';
import '../models/button.dart';

class ButtonEditorDialog extends StatefulWidget {
  final Button button;
  final Function(Button) onSave;

  const ButtonEditorDialog({
    super.key,
    required this.button,
    required this.onSave,
  });

  @override
  State<ButtonEditorDialog> createState() => _ButtonEditorDialogState();
}

class _ButtonEditorDialogState extends State<ButtonEditorDialog> {
  late TextEditingController _labelController;
  late String _selectedIcon;
  late Color _backgroundColor;
  late Color _textColor;
  late double _width;
  late double _height;

  final List<Map<String, dynamic>> _availableIcons = [
    {'name': 'power', 'icon': Icons.power_settings_new, 'label': 'Power'},
    {'name': 'volume_up', 'icon': Icons.volume_up, 'label': 'Volume Up'},
    {'name': 'volume_down', 'icon': Icons.volume_down, 'label': 'Volume Down'},
    {'name': 'volume_off', 'icon': Icons.volume_off, 'label': 'Mute'},
    {'name': 'keyboard_arrow_up', 'icon': Icons.keyboard_arrow_up, 'label': 'Up'},
    {'name': 'keyboard_arrow_down', 'icon': Icons.keyboard_arrow_down, 'label': 'Down'},
    {'name': 'keyboard_arrow_left', 'icon': Icons.keyboard_arrow_left, 'label': 'Left'},
    {'name': 'keyboard_arrow_right', 'icon': Icons.keyboard_arrow_right, 'label': 'Right'},
    {'name': 'menu', 'icon': Icons.menu, 'label': 'Menu'},
    {'name': 'check_circle', 'icon': Icons.check_circle, 'label': 'OK'},
    {'name': 'play_arrow', 'icon': Icons.play_arrow, 'label': 'Play'},
    {'name': 'pause', 'icon': Icons.pause, 'label': 'Pause'},
    {'name': 'stop', 'icon': Icons.stop, 'label': 'Stop'},
    {'name': 'fast_forward', 'icon': Icons.fast_forward, 'label': 'Fast Forward'},
    {'name': 'fast_rewind', 'icon': Icons.fast_rewind, 'label': 'Rewind'},
    {'name': 'skip_next', 'icon': Icons.skip_next, 'label': 'Next'},
    {'name': 'skip_previous', 'icon': Icons.skip_previous, 'label': 'Previous'},
    {'name': 'home', 'icon': Icons.home, 'label': 'Home'},
    {'name': 'settings', 'icon': Icons.settings, 'label': 'Settings'},
  ];

  final List<Color> _predefinedColors = [
    Colors.red,
    Colors.green,
    Colors.blue,
    Colors.orange,
    Colors.purple,
    Colors.teal,
    Colors.indigo,
    Colors.pink,
    Colors.brown,
    Colors.grey,
  ];

  @override
  void initState() {
    super.initState();
    _labelController = TextEditingController(text: widget.button.label);
    _selectedIcon = widget.button.icon ?? 'power';
    _backgroundColor = _parseColor(widget.button.style.bgColor);
    _textColor = _parseColor(widget.button.style.textColor);
    _width = widget.button.bbox.w;
    _height = widget.button.bbox.h;
  }

  Color _parseColor(String colorString) {
    try {
      return Color(int.parse(colorString.replaceFirst('#', '0xff')));
    } catch (e) {
      return Colors.blue;
    }
  }

  String _colorToHex(Color color) {
    return '#${color.value.toRadixString(16).substring(2)}';
  }

  void _saveButton() {
    final updatedButton = Button(
      id: widget.button.id,
      label: _labelController.text.trim(),
      icon: _selectedIcon,
      bbox: BoundingBox(
        x: widget.button.bbox.x,
        y: widget.button.bbox.y,
        w: _width,
        h: _height,
      ),
      style: Style(
        bgColor: _colorToHex(_backgroundColor),
        textColor: _colorToHex(_textColor),
      ),
      ir: widget.button.ir,
    );

    widget.onSave(updatedButton);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        padding: const EdgeInsets.all(16),
        constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Edit Button',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            
            // Button preview
            Container(
              height: 80,
              decoration: BoxDecoration(
                color: _backgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey),
              ),
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _availableIcons.firstWhere(
                        (icon) => icon['name'] == _selectedIcon,
                        orElse: () => _availableIcons.first,
                      )['icon'],
                      color: _textColor,
                      size: 24,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _labelController.text.isEmpty ? 'Button' : _labelController.text,
                      style: TextStyle(
                        color: _textColor,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            
            // Label input
            TextField(
              controller: _labelController,
              decoration: const InputDecoration(
                labelText: 'Button Label',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) => setState(() {}),
            ),
            const SizedBox(height: 16),
            
            // Icon selection
            Text('Icon:', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 8),
            Container(
              height: 120,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: GridView.builder(
                padding: const EdgeInsets.all(8),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 5,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: _availableIcons.length,
                itemBuilder: (context, index) {
                  final iconData = _availableIcons[index];
                  final isSelected = iconData['name'] == _selectedIcon;
                  
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedIcon = iconData['name'];
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected ? Colors.blue.withOpacity(0.2) : null,
                        border: Border.all(
                          color: isSelected ? Colors.blue : Colors.grey,
                          width: isSelected ? 2 : 1,
                        ),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Icon(
                        iconData['icon'],
                        size: 20,
                        color: isSelected ? Colors.blue : Colors.grey[600],
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            
            // Colors
            Text('Background Color:', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: _predefinedColors.map((color) {
                final isSelected = color.value == _backgroundColor.value;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _backgroundColor = color;
                    });
                  },
                  child: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected ? Colors.black : Colors.grey,
                        width: isSelected ? 3 : 1,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
            
            // Size controls
            Text('Size:', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Width: ${_width.round()}'),
                      Slider(
                        value: _width,
                        min: 40,
                        max: 120,
                        divisions: 16,
                        onChanged: (value) {
                          setState(() {
                            _width = value;
                          });
                        },
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Height: ${_height.round()}'),
                      Slider(
                        value: _height,
                        min: 30,
                        max: 80,
                        divisions: 10,
                        onChanged: (value) {
                          setState(() {
                            _height = value;
                          });
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: _saveButton,
                  child: const Text('Save'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _labelController.dispose();
    super.dispose();
  }
}
