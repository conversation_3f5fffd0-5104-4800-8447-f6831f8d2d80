{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\remote_designer\\build\\.cxx\\debug\\j3r176j1\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\remote_designer\\build\\.cxx\\debug\\j3r176j1\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}